/**
 * Node Handler Service
 * 
 * Handles processing of different node types with proper validation and context management
 */

import { ConversationContext, OutgoingMessage } from '../types';
import { FlowNode, FeedbackType, NotificationChannel, ChannelSupport } from '../types/enum';
import { ContextManagerService } from './context-manager.service';
import { logger } from '@neuratalk/common';

export class NodeHandlerService {
  constructor(private readonly contextManager: ContextManagerService) {}

  /**
   * Validate and process feedback node
   */
  async processFeedbackNode(
    node: OutgoingMessage,
    context: ConversationContext
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    if (node.nodeType !== FlowNode.FEEDBACK) {
      errors.push('Invalid node type for feedback processing');
      return { isValid: false, errors };
    }

    const { data } = node;
    const currentChannel = context.journeyContext.channelType || 'web';
    const currentLanguage = context.journeyContext.language || 'en';
    
    // Validate multi-language prompt
    if (!data.prompt || typeof data.prompt !== 'object') {
      errors.push('Prompt is required and must be a language object');
    } else {
      const promptText = data.prompt[currentLanguage] || data.prompt['en'];
      if (!promptText || promptText.trim().length === 0) {
        errors.push('Prompt is required for current language');
      } else if (promptText.length > 320) {
        errors.push('Prompt cannot exceed 320 characters');
      }
    }

    // Validate feedback type
    if (!data.feedbackType || !Object.values(FeedbackType).includes(data.feedbackType)) {
      errors.push('Valid feedback type is required');
    }

    // Multi-channel validation
    if (!data.channels || !data.channels[currentChannel]?.enabled) {
      errors.push(`Feedback node is not enabled for ${currentChannel} channel`);
    }

    // Channel-specific validation for feedback types
    if (data.feedbackType === FeedbackType.STAR_RATING || data.feedbackType === FeedbackType.THUMBS_UP) {
      if (currentChannel !== 'web' && currentChannel !== 'mobile') {
        errors.push(`${data.feedbackType} is only supported on web and mobile channels`);
      }
    }

    // Star rating validation
    if (data.feedbackType === FeedbackType.STAR_RATING) {
      if (!data.maxStars || data.maxStars < 1 || data.maxStars > 5) {
        errors.push('Star rating must allow between 1 and 5 stars');
      }
    }

    if (errors.length === 0) {
      const promptText = data.prompt[currentLanguage] || data.prompt['en'];
      await this.contextManager.setJourneyData(
        context.chatConversationId,
        context.currentJourneyId!,
        'feedbackConfig',
        {
          prompt: promptText,
          feedbackType: data.feedbackType,
          maxStars: data.maxStars,
          timestamp: new Date(),
          channel: currentChannel,
          language: currentLanguage
        }
      );

      logger.debug(`Feedback node configured: ${data.feedbackType} for ${context.chatConversationId}`);
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Validate and process notification node
   */
  async processNotificationNode(
    node: OutgoingMessage,
    context: ConversationContext
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    if (node.nodeType !== FlowNode.NOTIFICATION) {
      errors.push('Invalid node type for notification processing');
      return { isValid: false, errors };
    }

    const { data } = node;
    const currentChannel = context.journeyContext.channelType || 'web';
    const currentLanguage = context.journeyContext.language || 'en';

    // Multi-channel validation
    if (!data.channelConfig || !data.channelConfig[currentChannel]?.enabled) {
      errors.push(`Notification node is not enabled for ${currentChannel} channel`);
    }

    // Validate channels
    if (!data.channels || data.channels.length === 0) {
      errors.push('At least one notification channel must be selected');
    }

    // SMS validation with multi-language support
    if (data.channels.includes(NotificationChannel.SMS) || data.channels.includes(NotificationChannel.BOTH)) {
      if (!data.sms) {
        errors.push('SMS configuration is required when SMS channel is selected');
      } else {
        if (!data.sms.senderId) {
          errors.push('SMS sender ID is required');
        }
        if (!data.sms.recipientMsisdn) {
          errors.push('SMS recipient MSISDN is required');
        }
        if (!data.sms.messageBody || typeof data.sms.messageBody !== 'object') {
          errors.push('SMS message body is required and must be a language object');
        } else {
          const smsText = data.sms.messageBody[currentLanguage] || data.sms.messageBody['en'];
          if (!smsText || smsText.length > 320) {
            errors.push('SMS message body is required and cannot exceed 320 characters');
          }
        }
      }
    }

    // Email validation with multi-language support
    if (data.channels.includes(NotificationChannel.EMAIL) || data.channels.includes(NotificationChannel.BOTH)) {
      if (!data.email) {
        errors.push('Email configuration is required when email channel is selected');
      } else {
        if (!data.email.senderEmail) {
          errors.push('Email sender address is required');
        }
        if (!data.email.recipientEmail) {
          errors.push('Email recipient address is required');
        }
        if (!data.email.subject || typeof data.email.subject !== 'object') {
          errors.push('Email subject is required and must be a language object');
        }
        if (!data.email.messageBody || typeof data.email.messageBody !== 'object') {
          errors.push('Email message body is required and must be a language object');
        } else {
          const emailText = data.email.messageBody[currentLanguage] || data.email.messageBody['en'];
          if (!emailText || emailText.length > 320) {
            errors.push('Email message body is required and cannot exceed 320 characters');
          }
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (data.email.senderEmail && !emailRegex.test(data.email.senderEmail)) {
          errors.push('Invalid sender email format');
        }
        if (data.email.recipientEmail && !emailRegex.test(data.email.recipientEmail)) {
          errors.push('Invalid recipient email format');
        }
      }
    }

    if (errors.length === 0) {
      const smsText = data.sms?.messageBody?.[currentLanguage] || data.sms?.messageBody?.['en'];
      const emailSubject = data.email?.subject?.[currentLanguage] || data.email?.subject?.['en'];
      const emailText = data.email?.messageBody?.[currentLanguage] || data.email?.messageBody?.['en'];

      await this.contextManager.setJourneyData(
        context.chatConversationId,
        context.currentJourneyId!,
        'notificationConfig',
        {
          channels: data.channels,
          sms: data.sms ? { ...data.sms, messageBody: smsText } : undefined,
          email: data.email ? { ...data.email, subject: emailSubject, messageBody: emailText } : undefined,
          timestamp: new Date(),
          status: 'pending',
          channel: currentChannel,
          language: currentLanguage
        }
      );

      logger.debug(`Notification node configured for ${context.chatConversationId}`);
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Process agent transfer node
   */
  async processAgentTransferNode(
    node: OutgoingMessage,
    context: ConversationContext
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    if (node.nodeType !== FlowNode.AGENT_TRANSFER) {
      errors.push('Invalid node type for agent transfer processing');
      return { isValid: false, errors };
    }

    const { data } = node;
    const currentChannel = context.journeyContext.channelType || 'web';
    const currentLanguage = context.journeyContext.language || 'en';

    // Multi-channel validation
    if (!data.channels || !data.channels[currentChannel]?.enabled) {
      errors.push(`Agent transfer is not enabled for ${currentChannel} channel`);
      return { isValid: false, errors };
    }

    // Get localized transfer message
    const transferMessage = data.transferMessage?.[currentLanguage] || 
                           data.transferMessage?.['en'] || 
                           'Transferring to agent...';

    // Store agent transfer request in session context
    await this.contextManager.setSessionData(
      context.chatConversationId,
      'agentTransferRequested',
      {
        timestamp: new Date(),
        journeyId: context.currentJourneyId,
        transferMessage,
        channel: currentChannel,
        language: currentLanguage,
        channelConfig: data.channels[currentChannel]?.config
      }
    );

    logger.info(`Agent transfer requested for conversation ${context.chatConversationId}`);

    return { isValid: true, errors: [] };
  }

  /**
   * Process user feedback response
   */
  async processFeedbackResponse(
    conversationId: string,
    journeyId: string,
    userResponse: any
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    // Get feedback configuration
    const feedbackConfig = await this.contextManager.getJourneyData(
      conversationId,
      journeyId,
      'feedbackConfig'
    );

    if (!feedbackConfig) {
      errors.push('No feedback configuration found');
      return { isValid: false, errors };
    }

    // Validate response based on feedback type
    switch (feedbackConfig.feedbackType) {
      case FeedbackType.STAR_RATING:
        const rating = parseInt(userResponse);
        if (isNaN(rating) || rating < 1 || rating > (feedbackConfig.maxStars || 5)) {
          errors.push(`Rating must be between 1 and ${feedbackConfig.maxStars || 5}`);
        }
        break;

      case FeedbackType.THUMBS_UP:
        if (!['up', 'down', 'thumbs_up', 'thumbs_down'].includes(userResponse.toLowerCase())) {
          errors.push('Response must be thumbs up or thumbs down');
        }
        break;

      case FeedbackType.TEXT:
        if (!userResponse || userResponse.toString().trim().length === 0) {
          errors.push('Text feedback cannot be empty');
        }
        break;
    }

    if (errors.length === 0) {
      // Store feedback response
      await this.contextManager.setJourneyData(
        conversationId,
        journeyId,
        'feedbackResponse',
        {
          response: userResponse,
          feedbackType: feedbackConfig.feedbackType,
          timestamp: new Date(),
          processed: true
        }
      );

      // Also store in session for analytics
      await this.contextManager.setSessionData(
        conversationId,
        'lastFeedback',
        {
          response: userResponse,
          feedbackType: feedbackConfig.feedbackType,
          timestamp: new Date()
        }
      );

      logger.debug(`Feedback response processed: ${userResponse} for ${conversationId}`);
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Get node validation errors for UI
   */
  validateNodeConfiguration(nodeType: FlowNode, nodeData: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    switch (nodeType) {
      case FlowNode.FEEDBACK:
        if (!nodeData.prompt || nodeData.prompt.trim().length === 0) {
          errors.push('Prompt is required');
        }
        if (nodeData.prompt && nodeData.prompt.length > 320) {
          errors.push('Prompt cannot exceed 320 characters');
        }
        if (!nodeData.feedbackType) {
          errors.push('Feedback type is required');
        }
        break;

      case FlowNode.NOTIFICATION:
        if (!nodeData.channels || nodeData.channels.length === 0) {
          errors.push('At least one notification channel is required');
        }
        if (nodeData.channels?.includes(NotificationChannel.SMS) && !nodeData.sms?.messageBody) {
          errors.push('SMS message body is required');
        }
        if (nodeData.channels?.includes(NotificationChannel.EMAIL) && !nodeData.email?.messageBody) {
          errors.push('Email message body is required');
        }
        break;

      case FlowNode.AGENT_TRANSFER:
        // No validation required as per requirements
        break;
    }

    return { isValid: errors.length === 0, errors };
  }
}