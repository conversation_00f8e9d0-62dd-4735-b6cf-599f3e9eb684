/**
 * Integration Test Example
 * 
 * Demonstrates end-to-end functionality of the enhanced context management system
 * with new node types and flow connector scenarios.
 */

import { FlowEngine } from '../engine/flow-engine';
import { ContextManagerService } from '../services/context-manager.service';
import { NodeHandlerService } from '../services/node-handler.service';
import { RedisService } from '../services/redis.service';
import { DatabaseService } from '../services/database.service';
import { NLUService } from '../services/nlu.service';
import { FlowNode, FeedbackType, NotificationChannel } from '../types/enum';
import { ConversationContext, OutgoingMessage } from '../types';

export class IntegrationTestExample {
  private flowEngine: FlowEngine;
  private contextManager: ContextManagerService;
  private nodeHandler: NodeHandlerService;

  constructor(
    redisService: RedisService,
    databaseService: DatabaseService,
    nluService: NLUService
  ) {
    this.flowEngine = new FlowEngine(redisService, databaseService, nluService);
    this.contextManager = new ContextManagerService(redisService);
    this.nodeHandler = new NodeHandlerService(this.contextManager);
  }

  /**
   * Test complete journey with all node types
   */
  async testCompleteJourney(): Promise<void> {
    const conversationId = 'test-conversation-123';
    const journeyId = 'test-journey-456';

    console.log('🚀 Starting complete journey test...');

    // 1. Test feedback node processing
    await this.testFeedbackNode(conversationId, journeyId);

    // 2. Test notification node processing
    await this.testNotificationNode(conversationId, journeyId);

    // 3. Test agent transfer node processing
    await this.testAgentTransferNode(conversationId, journeyId);

    // 4. Test flow connector context preservation
    await this.testFlowConnectorScenario(conversationId);

    // 5. Test context cleanup
    await this.testContextCleanup(conversationId, journeyId);

    console.log('✅ Complete journey test finished successfully!');
  }

  /**
   * Test feedback node with different types
   */
  private async testFeedbackNode(conversationId: string, journeyId: string): Promise<void> {
    console.log('📝 Testing feedback node...');

    const starRatingNode: OutgoingMessage = {
      nodeType: FlowNode.FEEDBACK,
      data: {
        prompt: 'How would you rate our service?',
        feedbackType: FeedbackType.STAR_RATING,
        supportedChannels: ['web', 'mobile'],
        maxStars: 5
      }
    };

    const mockContext: Partial<ConversationContext> = {
      chatConversationId: conversationId,
      currentJourneyId: journeyId,
      journeyContext: {
        channelType: 'web',
        sessionTimeout: 3600
      }
    };

    const result = await this.nodeHandler.processFeedbackNode(
      starRatingNode,
      mockContext as ConversationContext
    );

    console.log(`Star rating validation: ${result.isValid ? '✅' : '❌'}`);

    // Test feedback response
    const feedbackResponse = await this.nodeHandler.processFeedbackResponse(
      conversationId,
      journeyId,
      '4'
    );

    console.log(`Feedback response validation: ${feedbackResponse.isValid ? '✅' : '❌'}`);
  }

  /**
   * Test notification node with SMS and Email
   */
  private async testNotificationNode(conversationId: string, journeyId: string): Promise<void> {
    console.log('📧 Testing notification node...');

    const notificationNode: OutgoingMessage = {
      nodeType: FlowNode.NOTIFICATION,
      data: {
        channels: [NotificationChannel.BOTH],
        sms: {
          senderId: 'TESTBOT',
          recipientMsisdn: '+1234567890',
          messageBody: 'Your request has been processed successfully.'
        },
        email: {
          senderEmail: '<EMAIL>',
          recipientEmail: '<EMAIL>',
          subject: 'Request Processed',
          messageBody: 'Your request has been processed successfully. Thank you!'
        }
      }
    };

    const mockContext: Partial<ConversationContext> = {
      chatConversationId: conversationId,
      currentJourneyId: journeyId,
      journeyContext: {
        channelType: 'web',
        sessionTimeout: 3600
      }
    };

    const result = await this.nodeHandler.processNotificationNode(
      notificationNode,
      mockContext as ConversationContext
    );

    console.log(`Notification validation: ${result.isValid ? '✅' : '❌'}`);
  }

  /**
   * Test agent transfer node
   */
  private async testAgentTransferNode(conversationId: string, journeyId: string): Promise<void> {
    console.log('👤 Testing agent transfer node...');

    const agentTransferNode: OutgoingMessage = {
      nodeType: FlowNode.AGENT_TRANSFER,
      data: {
        transferMessage: 'Connecting you to a live agent...',
        agentConfig: {
          department: 'support',
          priority: 'normal'
        }
      }
    };

    const mockContext: Partial<ConversationContext> = {
      chatConversationId: conversationId,
      currentJourneyId: journeyId,
      journeyContext: {
        channelType: 'web',
        sessionTimeout: 3600
      }
    };

    const result = await this.nodeHandler.processAgentTransferNode(
      agentTransferNode,
      mockContext as ConversationContext
    );

    console.log(`Agent transfer validation: ${result.isValid ? '✅' : '❌'}`);
  }

  /**
   * Test complex flow connector scenario with nested flows
   */
  private async testFlowConnectorScenario(conversationId: string): Promise<void> {
    console.log('🔗 Testing flow connector scenario...');

    // Test 10-level deep scenario with cleanup at level 8
    for (let level = 0; level <= 7; level++) {
      await this.contextManager.setFlowConnectorContext(
        conversationId,
        level,
        true,
        {
          levelData: `data_level_${level}`,
          timestamp: new Date()
        },
        level > 0 ? level - 1 : undefined
      );
    }

    // Level 8 with preserveContext = false - should trigger cleanup
    await this.contextManager.handleFlowConnectorCleanup(conversationId, 8, false);

    // Verify all levels 0-8 are cleared
    let allCleared = true;
    for (let level = 0; level <= 8; level++) {
      const data = await this.contextManager.getFlowConnectorContext(conversationId, level);
      if (data !== null) {
        allCleared = false;
        break;
      }
    }

    console.log(`All levels 0-8 cleared: ${allCleared ? '✅' : '❌'}`);
  }

  /**
   * Test context cleanup scenarios
   */
  private async testContextCleanup(conversationId: string, journeyId: string): Promise<void> {
    console.log('🧹 Testing context cleanup...');

    // Set up test data
    await this.contextManager.setJourneyData(conversationId, journeyId, 'testJourneyData', 'value');
    await this.contextManager.setSessionData(conversationId, 'testSessionData', 'value');
    await this.contextManager.setPluginUserInput('testPlugin', 'testInput', { test: true });

    // Test journey cleanup
    await this.contextManager.clearJourneyContext(conversationId, journeyId);

    const journeyDataAfterCleanup = await this.contextManager.getJourneyData(conversationId, journeyId, 'testJourneyData');
    const sessionDataAfterJourneyCleanup = await this.contextManager.getSessionData(conversationId, 'testSessionData');

    console.log(`Journey data cleared: ${!journeyDataAfterCleanup ? '✅' : '❌'}`);
    console.log(`Session data preserved: ${sessionDataAfterJourneyCleanup ? '✅' : '❌'}`);

    // Cleanup
    await this.contextManager.cleanupConversationContexts(conversationId);
    await this.contextManager.clearPluginContext('testPlugin');
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting comprehensive integration tests...\n');

    try {
      await this.testCompleteJourney();
      console.log('\n🎉 All integration tests completed successfully!');
    } catch (error) {
      console.error('❌ Integration test failed:', error);
      throw error;
    }
  }
}