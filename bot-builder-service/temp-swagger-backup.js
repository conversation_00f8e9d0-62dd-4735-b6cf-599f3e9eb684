"use strict";
/**
 * Swagger Configuration for Bot Builder Service
 *
 * OpenAPI 3.0 specification for all administrative APIs
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.swaggerSpec = void 0;
const swagger_jsdoc_1 = __importDefault(require("swagger-jsdoc"));
const zod_to_openapi_1 = require("@asteasolutions/zod-to-openapi");
const schemas_1 = require("../schemas");
const openApiGenerator = new zod_to_openapi_1.OpenApiGeneratorV3([
    {
        schema: schemas_1.CreateAppSchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateAppSchema,
        type: "schema",
    },
    {
        schema: schemas_1.AppIdParamSchema,
        type: "schema",
    },
    {
        schema: schemas_1.GetAppsQuerySchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateBotRequestSchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateBotRequestSchema,
        type: "schema",
    },
    {
        schema: schemas_1.BotIdParamSchema,
        type: "schema",
    },
    {
        schema: schemas_1.BotChannelParamSchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateChannelIntegrationSchema,
        type: "schema",
    },
    {
        schema: schemas_1.BotChannelIdParamSchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateChannelIntegrationSchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateFlowRequestSchema,
        type: "schema",
    },
    {
        schema: schemas_1.FlowIdParamSchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateFlowRequestSchema,
        type: "schema",
    },
    {
        schema: schemas_1.FlowAppIdParamSchema,
        type: "schema",
    },
    {
        schema: schemas_1.GetFlowsQuerySchema,
        type: "schema",
    },
    {
        schema: schemas_1.BulkCreateFlowsRequestSchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateFaqCategorySchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateFaqCategorySchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateFaqItemSchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateFaqItemSchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateFaqTranslationSchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateFaqTranslationSchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateIntentItemSchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateIntentItemSchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateIntentUtteranceSchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateIntentUtteranceSchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateIntentUtteranceTranslationSchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateIntentUtteranceTranslationSchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateEntitySchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateEntitySchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateLanguageSchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateLanguageSchema,
        type: "schema",
    },
    {
        schema: schemas_1.CreateBotLanguageSchema,
        type: "schema",
    },
    {
        schema: schemas_1.UpdateBotLanguageSchema,
        type: "schema",
    },
    // Response schemas
    {
        schema: schemas_1.ApiResponseSchema,
        type: "schema",
    },
    {
        schema: schemas_1.ErrorResponseSchema,
        type: "schema",
    },
    {
        schema: schemas_1.ValidationErrorResponseSchema,
        type: "schema",
    },
    {
        schema: schemas_1.NotFoundErrorResponseSchema,
        type: "schema",
    },
    {
        schema: schemas_1.InternalServerErrorResponseSchema,
        type: "schema",
    },
    {
        schema: schemas_1.DeletedResponseSchema,
        type: "schema",
    },
    // Model schemas
    {
        schema: schemas_1.LanguageSchema,
        type: "schema",
    },
    {
        schema: schemas_1.BotLanguageSchema,
        type: "schema",
    },
    {
        schema: schemas_1.EntitySchema,
        type: "schema",
    },
    {
        schema: schemas_1.FaqCategorySchema,
        type: "schema",
    },
    {
        schema: schemas_1.FaqItemSchema,
        type: "schema",
    },
    {
        schema: schemas_1.FaqTranslationSchema,
        type: "schema",
    },
    {
        schema: schemas_1.IntentItemSchema,
        type: "schema",
    },
    {
        schema: schemas_1.IntentUtteranceSchema,
        type: "schema",
    },
    {
        schema: schemas_1.IntentUtteranceTranslationSchema,
        type: "schema",
    },
    {
        schema: schemas_1.PaginationSchema,
        type: "schema",
    },
    {
        schema: schemas_1.PaginatedLanguagesSchema,
        type: "schema",
    },
    {
        schema: schemas_1.PaginatedBotLanguagesSchema,
        type: "schema",
    },
    {
        schema: schemas_1.PaginatedEntitiesSchema,
        type: "schema",
    },
    {
        schema: schemas_1.PaginatedFaqCategoriesSchema,
        type: "schema",
    },
    {
        schema: schemas_1.PaginatedFaqItemsSchema,
        type: "schema",
    },
    {
        schema: schemas_1.PaginatedFaqTranslationsSchema,
        type: "schema",
    },
    {
        schema: schemas_1.PaginatedIntentItemsSchema,
        type: "schema",
    },
    {
        schema: schemas_1.PaginatedIntentUtterancesSchema,
        type: "schema",
    },
    {
        schema: schemas_1.PaginatedIntentUtteranceTranslationsSchema,
        type: "schema",
    },
]);
const swaggerDefinition = {
    openapi: "3.0.0",
    info: {
        title: "Bot Builder Service API",
        version: "1.0.0",
        description: "Administrative API for managing bots, flows, and configurations in the no-code chatbot platform",
        contact: {
            name: "Chatbot Platform Team",
            email: "<EMAIL>",
        },
        license: {
            name: "MIT",
            url: "https://opensource.org/licenses/MIT",
        },
    },
    servers: [
        {
            url: "http://localhost:3000",
            description: "Development server",
        },
        {
            url: "https://api.chatbot-platform.com",
            description: "Production server",
        },
    ],
    components: {
        securitySchemes: {
            BearerAuth: {
                type: "http",
                scheme: "bearer",
                bearerFormat: "JWT",
                description: "JWT token for user authentication",
            },
        },
        ...openApiGenerator.generateComponents(),
    },
    tags: [
        {
            name: "Apps",
            description: "Application management operations",
        },
        {
            name: "Bots",
            description: "Bot management operations",
        },
        {
            name: "Flows",
            description: "Flow management operations",
        },
        {
            name: "Health",
            description: "Health check endpoints",
        },
        {
            name: "FAQ Categories",
            description: "FAQ category management operations",
        },
        {
            name: "FAQ Items",
            description: "FAQ item management operations",
        },
        {
            name: "FAQ Translations",
            description: "FAQ translation management operations",
        },
        {
            name: "Intent Items",
            description: "Intent item management operations",
        },
        {
            name: "Intent Utterances",
            description: "Intent utterance management operations",
        },
        {
            name: "Intent Utterance Translations",
            description: "Intent utterance translation management operations",
        },
        {
            name: "Entities",
            description: "Entity management operations",
        },
        {
            name: "Languages",
            description: "Language management operations",
        },
        {
            name: "Bot Languages",
            description: "Bot language configuration operations",
        },
    ],
};
const options = {
    definition: swaggerDefinition,
    apis: ["./src/controllers/*.ts", "./src/routers/*.ts", "./src/app.ts"],
    swaggerOptions: {
        persistAuthorization: true,
    },
};
exports.swaggerSpec = (0, swagger_jsdoc_1.default)(options);
exports.default = exports.swaggerSpec;
//# sourceMappingURL=swagger.js.map