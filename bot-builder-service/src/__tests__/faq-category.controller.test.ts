import { Request, Response } from "express";
import { FaqCategoryController } from "../controllers/faq-category.controller";

jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  logger: { info: jest.fn(), error: jest.fn(), warn: jest.fn(), debug: jest.fn() },
  successResponse: jest.fn((data) => ({ success: true, data, timestamp: new Date() })),
  errorResponse: jest.fn((error) => ({ success: false, error: { code: error.code, message: error.message }, timestamp: new Date() })),
}));

const { getPaginatedResults, successResponse, errorResponse } = require("@neuratalk/common");

describe("FaqCategoryController", () => {
  let controller: FaqCategoryController;
  let mockModels: any;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockContext: any;

  beforeEach(() => {
    mockModels = {
      FaqCategory: { create: jest.fn(), findOne: jest.fn(), findByPk: jest.fn(), update: jest.fn(), destroy: jest.fn() },
    };

    mockContext = {
      db: { models: mockModels, transaction: jest.fn((cb) => cb({})), healthCheck: jest.fn() },
    };

    controller = new FaqCategoryController(mockContext);
    mockReq = { body: {}, params: {}, query: {}, user: { id: "user-123" } };
    mockRes = { status: jest.fn().mockReturnThis(), json: jest.fn().mockReturnThis(), send: jest.fn().mockReturnThis() };
    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create FAQ category successfully", async () => {
      const mockFaqCategory = { id: "cat-123", name: "Test Category" };
      mockReq.body = { name: "Test Category", botId: "bot-123" };
      mockModels.FaqCategory.create.mockResolvedValue(mockFaqCategory);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockModels.FaqCategory.create).toHaveBeenCalledWith({
        name: "Test Category", botId: "bot-123", createdBy: "user-123", updatedBy: "user-123"
      });
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith(mockFaqCategory);
    });

    it("should handle creation error", async () => {
      mockReq.body = { name: "Test Category" };
      mockModels.FaqCategory.create.mockRejectedValue(new Error("Creation failed"));

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({ error: expect.any(Error), code: "VALIDATION_ERROR" });
    });
  });

  describe("getAll", () => {
    it("should retrieve all FAQ categories", async () => {
      const mockResult = { items: [{ id: "cat-1" }], pagination: {} };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(getPaginatedResults).toHaveBeenCalledWith(mockModels.FaqCategory, mockReq.query, ["name"]);
      expect(successResponse).toHaveBeenCalledWith(mockResult);
    });

    it("should handle getAll error", async () => {
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error), code: "INTERNAL_ERROR", message: "Failed to fetch FAQ categories"
      });
    });
  });

  describe("getById", () => {
    it("should retrieve FAQ category by ID", async () => {
      const mockFaqCategory = { id: "cat-123", name: "Test Category" };
      mockReq.params = { id: "cat-123" };
      mockModels.FaqCategory.findOne.mockResolvedValue(mockFaqCategory);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockModels.FaqCategory.findOne).toHaveBeenCalledWith({ where: { id: "cat-123" } });
      expect(successResponse).toHaveBeenCalledWith(mockFaqCategory);
    });

    it("should return 404 for non-existent FAQ category", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.FaqCategory.findOne.mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({ code: "NOT_FOUND", message: "FAQ category not found" });
    });

    it("should handle getById error", async () => {
      mockReq.params = { id: "cat-123" };
      mockModels.FaqCategory.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error), code: "INTERNAL_ERROR", message: "Failed to fetch FAQ category"
      });
    });
  });

  describe("update", () => {
    it("should update FAQ category successfully", async () => {
      const mockFaqCategory = { id: "cat-123", name: "Updated Category" };
      mockReq.params = { id: "cat-123" };
      mockReq.body = { name: "Updated Category" };
      mockModels.FaqCategory.update.mockResolvedValue([1]);
      mockModels.FaqCategory.findByPk.mockResolvedValue(mockFaqCategory);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockModels.FaqCategory.update).toHaveBeenCalledWith(
        { name: "Updated Category", updatedBy: "user-123" }, { where: { id: "cat-123" } }
      );
      expect(successResponse).toHaveBeenCalledWith(mockFaqCategory);
    });

    it("should return 404 if FAQ category not found during update", async () => {
      mockReq.params = { id: "non-existent" };
      mockReq.body = { name: "Updated Category" };
      mockModels.FaqCategory.update.mockResolvedValue([0]);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({ code: "NOT_FOUND", message: "FAQ category not found" });
    });

    it("should handle update error", async () => {
      mockReq.params = { id: "cat-123" };
      mockReq.body = { name: "Updated Category" };
      mockModels.FaqCategory.update.mockRejectedValue(new Error("Update failed"));

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({ error: expect.any(Error), code: "VALIDATION_ERROR" });
    });
  });

  describe("delete", () => {
    it("should delete FAQ category successfully", async () => {
      mockReq.params = { id: "cat-123" };
      mockModels.FaqCategory.destroy.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockModels.FaqCategory.destroy).toHaveBeenCalledWith({ where: { id: "cat-123" } });
      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.send).toHaveBeenCalled();
    });

    it("should return 404 if FAQ category not found during delete", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.FaqCategory.destroy.mockResolvedValue(0);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({ code: "NOT_FOUND", message: "FAQ category not found" });
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "cat-123" };
      mockModels.FaqCategory.destroy.mockRejectedValue(new Error("Delete failed"));

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error), code: "INTERNAL_ERROR", message: "Failed to delete FAQ category"
      });
    });
  });
});