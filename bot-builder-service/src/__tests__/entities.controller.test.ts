import { Request, Response } from "express";
import { EntitiesController } from "../controllers/entities.controller";
import { WhereOptions } from "sequelize";

jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  successResponse: jest.fn((data) => ({ success: true, data, timestamp: new Date() })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
    timestamp: new Date(),
  })),
}));

const { getPaginatedResults } = require("@neuratalk/common");

describe("EntitiesController", () => {
  let controller: EntitiesController;
  let mockModels: any;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;

  beforeEach(() => {
    mockModels = {
      Entities: {
        create: jest.fn(),
        findOne: jest.fn(),
        findByPk: jest.fn(),
        update: jest.fn(),
        destroy: jest.fn(),
      },
    };

    controller = new EntitiesController({
      db: {
        models: mockModels,
      },
    } as any);

    mockReq = {
      body: {},
      params: {},
      query: {},
      user: { id: "user-123" },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create entity successfully", async () => {
      const mockEntity = { id: "entity-123", name: "city", botId: "bot-123" };
      mockReq.body = { name: "city", botId: "bot-123" };
      mockModels.Entities.create.mockResolvedValue(mockEntity);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockModels.Entities.create).toHaveBeenCalledWith({
        name: "city",
        botId: "bot-123",
        createdBy: "user-123",
        updatedBy: "user-123",
      });
      expect(mockRes.status).toHaveBeenCalledWith(201);
    });

    it("should create entity with intentId", async () => {
      const mockEntity = {
        id: "entity-123",
        name: "subcity",
        botId: "bot-123",
        intentId: "intent-123",
      };
      mockReq.body = { name: "subcity", botId: "bot-123", intentId: "intent-123" };
      mockModels.Entities.create.mockResolvedValue(mockEntity);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockModels.Entities.create).toHaveBeenCalledWith({
        name: "subcity",
        botId: "bot-123",
        intentId: "intent-123",
        createdBy: "user-123",
        updatedBy: "user-123",
      });
    });

    it("should handle creation error", async () => {
      mockReq.body = { name: "city", botId: "bot-123" };
      mockModels.Entities.create.mockRejectedValue(new Error("Creation failed"));

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });

  describe("getAll", () => {
    it("should get paginated entities", async () => {
      const mockResult = {
        items: [{ id: "entity-1" }, { id: "entity-2" }],
        pagination: { page: 1, limit: 20, total: 2, totalPages: 1, hasNext: false, hasPrev: false },
      };
      mockReq.query = {
        page: "1",
        limit: "20",
        filter: JSON.stringify({ botId: { eq: "bot-123" } }),
      };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(getPaginatedResults).toHaveBeenCalledWith(mockModels.Entities, mockReq.query, [
        "name",
      ]);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockResult,
        timestamp: expect.any(Date),
      });
    });

    it("should handle getAll error", async () => {
      mockReq.query = { page: "1", limit: "20" };
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("getById", () => {
    it("should get entity by id", async () => {
      const mockEntity = { id: "entity-123", name: "city" };
      mockReq.params = { id: "entity-123" };
      mockModels.Entities.findOne.mockResolvedValue(mockEntity);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockModels.Entities.findOne).toHaveBeenCalledWith({
        where: { id: "entity-123" },
      });
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockEntity,
        timestamp: expect.any(Date),
      });
    });

    it("should return 404 when entity not found", async () => {
      mockReq.params = { id: "entity-123" };
      mockModels.Entities.findOne.mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle getById error", async () => {
      mockReq.params = { id: "entity-123" };
      mockModels.Entities.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("update", () => {
    it("should update entity successfully", async () => {
      const mockUpdatedEntity = { id: "entity-123", name: "Updated city" };
      mockReq.params = { id: "entity-123" };
      mockReq.body = { name: "Updated city" };
      mockModels.Entities.update.mockResolvedValue([1]);
      mockModels.Entities.findByPk.mockResolvedValue(mockUpdatedEntity);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockModels.Entities.update).toHaveBeenCalledWith(
        { name: "Updated city", updatedBy: "user-123" },
        { where: { id: "entity-123" } },
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockUpdatedEntity,
        timestamp: expect.any(Date),
      });
    });

    it("should return 404 when updating non-existent entity", async () => {
      mockReq.params = { id: "entity-123" };
      mockReq.body = { name: "Updated city" };
      mockModels.Entities.update.mockResolvedValue([0]);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle update error", async () => {
      mockReq.params = { id: "entity-123" };
      mockReq.body = { name: "Updated city" };
      mockModels.Entities.update.mockRejectedValue(new Error("Update failed"));

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });

    it("should handle missing user in update request", async () => {
      const mockUpdatedEntity = { id: "entity-123", name: "Updated city" };
      mockReq.params = { id: "entity-123" };
      mockReq.body = { name: "Updated city" };
      mockReq.user = undefined;
      mockModels.Entities.update.mockResolvedValue([1]);
      mockModels.Entities.findByPk.mockResolvedValue(mockUpdatedEntity);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockModels.Entities.update).toHaveBeenCalledWith(
        { name: "Updated city", updatedBy: undefined },
        { where: { id: "entity-123" } },
      );
    });
  });

  describe("delete", () => {
    it("should delete entity successfully", async () => {
      mockReq.params = { id: "entity-123" };
      mockModels.Entities.destroy.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockModels.Entities.destroy).toHaveBeenCalledWith({
        where: { id: "entity-123" },
      });
      expect(mockRes.status).toHaveBeenCalledWith(204);
    });

    it("should return 404 when deleting non-existent entity", async () => {
      mockReq.params = { id: "entity-123" };
      mockModels.Entities.destroy.mockResolvedValue(0);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "entity-123" };
      mockModels.Entities.destroy.mockRejectedValue(new Error("Delete failed"));

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });
});
