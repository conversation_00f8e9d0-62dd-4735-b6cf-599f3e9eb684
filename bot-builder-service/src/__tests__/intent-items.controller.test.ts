import { Request, Response } from "express";
import { IntentItemsController } from "../controllers/intent-items.controller";

jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  successResponse: jest.fn((data) => ({ success: true, data, timestamp: new Date() })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
    timestamp: new Date(),
  })),
}));

const { getPaginatedResults } = require("@neuratalk/common");

describe("IntentItemsController", () => {
  let controller: IntentItemsController;
  let mockModels: any;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;

  beforeEach(() => {
    mockModels = {
      IntentItems: {
        create: jest.fn(),
        findOne: jest.fn(),
        findByPk: jest.fn(),
        update: jest.fn(),
        destroy: jest.fn(),
      },
    };

    controller = new IntentItemsController({
      db: {
        models: mockModels,
        transaction: jest.fn((cb) => cb({})),
      },
    } as any);

    mockReq = {
      body: {},
      params: {},
      query: {},
      user: { id: "user-123" },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create intent item successfully", async () => {
      const mockIntentItem = { id: "intent-123", name: "test_intent" };
      mockReq.body = { botId: "bot-123", flowId: "flow-123", name: "test_intent", description: "Test description" };
      mockModels.IntentItems.create.mockResolvedValue(mockIntentItem);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockModels.IntentItems.create).toHaveBeenCalledWith({
        botId: "bot-123",
        flowId: "flow-123",
        name: "test_intent",
        description: "Test description",
        createdBy: "user-123",
        updatedBy: "user-123",
      });
      expect(mockRes.status).toHaveBeenCalledWith(201);
    });

    it("should handle creation error", async () => {
      mockReq.body = { botId: "bot-123", examples: [] };
      mockModels.IntentItems.create.mockRejectedValue(new Error("Creation failed"));

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });

  describe("getAll", () => {
    it("should get paginated intent items", async () => {
      const mockResult = {
        items: [{ id: "intent-1" }, { id: "intent-2" }],
        pagination: { page: 1, limit: 20, total: 2, totalPages: 1, hasNext: false, hasPrev: false },
      };
      mockReq.query = { page: "1", limit: "20", filter: JSON.stringify({ botId: { eq: "bot-123" } }) };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);

      await controller.getAll(mockReq as any, mockRes as any);

      expect(getPaginatedResults).toHaveBeenCalledWith(
        mockModels.IntentItems,
        mockReq.query,
        ["name"],
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockResult,
        timestamp: expect.any(Date),
      });
    });

    it("should handle getAll error", async () => {
      mockReq.query = { page: "1", limit: "20" };
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("getById", () => {
    it("should get intent item by id", async () => {
      const mockIntentItem = { id: "intent-123", botId: "bot-123", examples: [] };
      mockReq.params = { id: "intent-123" };
      mockModels.IntentItems.findOne.mockResolvedValue(mockIntentItem);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockModels.IntentItems.findOne).toHaveBeenCalledWith({
        where: { id: "intent-123" },
      });
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockIntentItem,
        timestamp: expect.any(Date),
      });
    });

    it("should return 404 when intent item not found", async () => {
      mockReq.params = { id: "intent-123" };
      mockModels.IntentItems.findOne.mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle getById error", async () => {
      mockReq.params = { id: "intent-123" };
      mockModels.IntentItems.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("update", () => {
    it("should update intent item successfully", async () => {
      const mockUpdatedIntent = { id: "intent-123", name: "Updated Intent" };
      mockReq.params = { id: "intent-123" };
      mockReq.body = { name: "Updated Intent" };
      mockModels.IntentItems.update.mockResolvedValue([1]);
      mockModels.IntentItems.findByPk.mockResolvedValue(mockUpdatedIntent);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockModels.IntentItems.update).toHaveBeenCalledWith(
        { name: "Updated Intent", updatedBy: "user-123" },
        { where: { id: "intent-123" } },
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockUpdatedIntent,
        timestamp: expect.any(Date),
      });
    });

    it("should return 404 when updating non-existent intent item", async () => {
      mockReq.params = { id: "intent-123" };
      mockReq.body = { examples: [{ id: "entity-1", name: "city", value: "Updated London" }] };
      mockModels.IntentItems.update.mockResolvedValue([0]);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle update error", async () => {
      mockReq.params = { id: "intent-123" };
      mockReq.body = { examples: [{ id: "entity-1", name: "city", value: "Updated London" }] };
      mockModels.IntentItems.update.mockRejectedValue(new Error("Update failed"));

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });

  describe("delete", () => {
    it("should delete intent item successfully", async () => {
      mockReq.params = { id: "intent-123" };
      mockModels.IntentItems.destroy.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockModels.IntentItems.destroy).toHaveBeenCalledWith({
        where: { id: "intent-123" },
      });
      expect(mockRes.status).toHaveBeenCalledWith(204);
    });

    it("should return 404 when deleting non-existent intent item", async () => {
      mockReq.params = { id: "intent-123" };
      mockModels.IntentItems.destroy.mockResolvedValue(0);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "intent-123" };
      mockModels.IntentItems.destroy.mockRejectedValue(new Error("Delete failed"));

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("assignFlowToIntent", () => {
    it("should assign flow to intent successfully", async () => {
      const mockIntent = { id: "intent-123", botId: "bot-123", flowId: null, update: jest.fn() };
      mockModels.IntentItems.findByPk.mockResolvedValue(mockIntent);
      mockModels.IntentItems.update.mockResolvedValue([1]);

      mockReq.body = { intentId: "intent-123", flowId: "flow-456" };

      await controller.assignFlowToIntent(mockReq as any, mockRes as Response);

      expect(mockModels.IntentItems.update).toHaveBeenCalledWith(
        { flowId: null, updatedBy: "user-123" },
        { where: { flowId: "flow-456", botId: "bot-123" }, transaction: expect.any(Object) },
      );
      expect(mockIntent.update).toHaveBeenCalledWith(
        { flowId: "flow-456", updatedBy: "user-123" },
        { transaction: expect.any(Object) },
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: { message: "Flow assigned successfully" },
        timestamp: expect.any(Date),
      });
    });

    it("should not update if flow is already assigned to the intent", async () => {
      const mockIntent = { id: "intent-123", botId: "bot-123", flowId: "flow-456", update: jest.fn() };
      mockModels.IntentItems.findByPk.mockResolvedValue(mockIntent);

      mockReq.body = { intentId: "intent-123", flowId: "flow-456" };

      await controller.assignFlowToIntent(mockReq as any, mockRes as Response);

      expect(mockModels.IntentItems.update).not.toHaveBeenCalled();
      expect(mockIntent.update).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
    });

    it("should return 400 if intent not found", async () => {
      mockModels.IntentItems.findByPk.mockResolvedValue(null);

      mockReq.body = { intentId: "non-existent-intent", flowId: "flow-456" };

      await controller.assignFlowToIntent(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: "ASSIGN_FLOW_ERROR",
          message: "Intent with ID non-existent-intent not found.",
        },
        timestamp: expect.any(Date),
      });
    });

    it("should handle error during assignment", async () => {
      mockModels.IntentItems.findByPk.mockRejectedValue(new Error("Database error"));

      mockReq.body = { intentId: "intent-123", flowId: "flow-456" };

      await controller.assignFlowToIntent(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: "ASSIGN_FLOW_ERROR",
          message: "Database error",
        },
        timestamp: expect.any(Date),
      });
    });
  });
});
