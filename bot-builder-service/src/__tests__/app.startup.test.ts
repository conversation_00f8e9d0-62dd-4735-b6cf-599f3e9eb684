import { App } from "../app";
import config from "../config";
import { logger } from "@neuratalk/common";

jest.mock("../config", () => ({
  __esModule: true,
  default: {
    server: {
      port: 3000,
      env: "test",
      corsOrigins: ["http://localhost:3000"],
    },
    kafka: {
      brokers: ["localhost:9092"],
    },
    database: {},
    security: {},
    services: {},
    logging: {},
  },
  ADMIN_USER_ID: "test-admin-id",
}));

const mockDatabaseConnectionInstance = {
  connect: jest.fn().mockResolvedValue(undefined),
  disconnect: jest.fn().mockResolvedValue(undefined),
  models: {},
  sequelize: {},
  healthCheck: jest.fn(),
};

jest.mock("@neuratalk/bot-store", () => ({
  DatabaseConnection: jest.fn().mockImplementation(() => mockDatabaseConnectionInstance),
}));

jest.mock("api_gw", () => ({
  initializeApiGw: jest.fn().mockResolvedValue(undefined),
  getStudioAppsService: jest.fn().mockReturnValue({
    createAppInfo: jest.fn(),
    updateAppInfo: jest.fn(),
    getAllApps: jest.fn(),
    getAppInfo: jest.fn(),
    purgeAppInfo: jest.fn(),
    check4Blacklist: jest.fn((req, res, next) => next()),
    validateCreateAppInfo: jest.fn((req, res, next) => next()),
    checkForDuplicateName: jest.fn((req, res, next) => next()),
  }),
}));

jest.mock("../services/bot.service", () => ({
  BotService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/flow.service", () => ({
  FlowService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/rasa.service", () => ({
  RasaService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/build.service", () => ({
  BuildService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../handlers/training-result.handler", () => ({
  TrainingResultHandler: jest.fn().mockImplementation(() => ({
    handleMessage: jest.fn(),
  })),
}));

jest.mock("../routers/index.router", () => ({
  createRoutes: jest.fn().mockReturnValue(jest.fn()),
}));

jest.mock("../middleware/auth.middleware", () => ({
  authMiddleware: jest.fn((req, res, next) => next()),
}));

jest.mock("@neuratalk/common", () => ({
  ...jest.requireActual("@neuratalk/common"),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  KafkaProducer: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
  })),
  KafkaConsumer: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    subscribeAndRun: jest.fn().mockResolvedValue(undefined),
  })),
}));

describe("App Startup Simulation", () => {
  let appInstance: App;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(process, "exit").mockImplementation((() => {}) as any);
    appInstance = new App();
    jest.spyOn(mockDatabaseConnectionInstance, "connect");
    jest.spyOn(mockDatabaseConnectionInstance, "disconnect");
    jest.spyOn(appInstance.app, "listen").mockImplementation((port, callback) => {
      if (callback) {
        // Execute callback immediately to ensure log messages are captured
        callback();
      }
      return {} as any; // Mock the server object
    });
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  it("should initialize core services and start the server", async () => {
    await appInstance.start();

    expect(mockDatabaseConnectionInstance.connect).toHaveBeenCalled();
    expect(logger.info).toHaveBeenCalledWith("Core services initialized.");
    expect(logger.info).toHaveBeenCalledWith(
      `Bot Builder Service started on port ${config.server.port}`,
    );
  });

  it("should handle startup errors gracefully", async () => {
    mockDatabaseConnectionInstance.connect.mockRejectedValueOnce(new Error("DB connection failed"));

    await appInstance.start();

    expect(mockDatabaseConnectionInstance.connect).toHaveBeenCalled();
    expect(logger.error).toHaveBeenCalledWith("Failed to start application:", expect.any(Error));
    expect(process.exit).toHaveBeenCalledWith(1);
  });

  it("should handle graceful shutdown", async () => {
    await appInstance.start(); // Start the app first
    await appInstance.stop();

    expect(mockDatabaseConnectionInstance.disconnect).toHaveBeenCalled();
    expect(logger.info).toHaveBeenCalledWith("Shutting down Bot Builder Service...");
    expect(logger.info).toHaveBeenCalledWith("Bot Builder Service stopped");
  });

  it("should log error during shutdown", async () => {
    await appInstance.start(); // Start the app first
    mockDatabaseConnectionInstance.disconnect.mockRejectedValueOnce(new Error("DB disconnect failed"));

    await appInstance.stop();

    expect(mockDatabaseConnectionInstance.disconnect).toHaveBeenCalled();
    expect(logger.error).toHaveBeenCalledWith("Error during shutdown:", expect.any(Error));
  });
});
