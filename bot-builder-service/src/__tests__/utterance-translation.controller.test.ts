import { Request, Response } from "express";
import { UtteranceTranslationController } from "../controllers/utterance-translation.controller";

jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  logger: { info: jest.fn(), error: jest.fn(), warn: jest.fn(), debug: jest.fn() },
  successResponse: jest.fn((data) => ({ success: true, data, timestamp: new Date() })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
    timestamp: new Date(),
  })),
}));
jest.mock("@neuratalk/bot-store", () => ({
  parseIncludeQuery: jest.fn(),
}));

const { getPaginatedResults, successResponse, errorResponse } = require("@neuratalk/common");
const { parseIncludeQuery } = require("@neuratalk/bot-store");

describe("UtteranceTranslationController", () => {
  let controller: UtteranceTranslationController;
  let mockModels: any;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockContext: any;

  beforeEach(() => {
    mockModels = {
      IntentUtterance: { create: jest.fn(), destroy: jest.fn() },
      UtteranceTranslation: {
        create: jest.fn(),
        findOne: jest.fn(),
        findByPk: jest.fn(),
        update: jest.fn(),
        destroy: jest.fn(),
        count: jest.fn(),
        findAll: jest.fn(),
      },
    };

    mockContext = {
      db: { models: mockModels, transaction: jest.fn((cb) => cb({})) },
    };

    controller = new UtteranceTranslationController(mockContext);
    mockReq = { body: {}, params: {}, query: {}, user: { id: "user-123" } };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };
    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create new utterance and translation successfully", async () => {
      const mockUtterance = { id: "utterance-123" };
      const mockTranslation = { id: "trans-123" };
      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      mockReq.body = { text: "Hello" };
      mockModels.IntentUtterance.create.mockResolvedValue(mockUtterance);
      mockModels.UtteranceTranslation.create.mockResolvedValue(mockTranslation);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith(mockTranslation);
    });

    it("should create translation for existing utterance", async () => {
      const mockTranslation = { id: "trans-123" };
      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      mockReq.body = { utteranceId: "utterance-existing", text: "Hello" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue(null);
      mockModels.UtteranceTranslation.create.mockResolvedValue(mockTranslation);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(201);
    });

    it("should return 400 if translation already exists", async () => {
      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      mockReq.body = { utteranceId: "utterance-existing", text: "Hello" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue({});

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });

    it("should handle creation error", async () => {
      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      mockReq.body = { text: "Hello" };
      mockModels.IntentUtterance.create.mockRejectedValue(new Error("Creation failed"));

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });

  describe("getAll", () => {
    it("should retrieve all utterance translations", async () => {
      const mockResult = {
        items: [
          {
            id: "utterance-1",
            utteranceTranslations: [
              { toJSON: () => ({ id: "trans-1", utteranceId: "utterance-1", langId: "lang-1" }) },
            ],
          },
        ],
      };
      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);
      (parseIncludeQuery as jest.Mock).mockReturnValue([{}]);
      mockModels.UtteranceTranslation.findAll.mockResolvedValue([
        { utteranceId: "utterance-1", langId: "lang-2", language: { name: "Spanish", code: "es" } },
      ]);

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(successResponse).toHaveBeenCalled();
    });

    it("should handle getAll error", async () => {
      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("getById", () => {
    it("should retrieve utterance translation by ID", async () => {
      const mockTranslation = { id: "trans-123" };
      mockReq.params = { id: "trans-123" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue(mockTranslation);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(successResponse).toHaveBeenCalledWith(mockTranslation);
    });

    it("should return 404 for non-existent translation", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle getById error", async () => {
      mockReq.params = { id: "trans-123" };
      mockModels.UtteranceTranslation.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("getTranslationsByUtteranceId", () => {
    it("should retrieve translations by utterance ID", async () => {
      const mockTranslations = [{ id: "trans-1" }];
      mockReq.params = { utteranceId: "utterance-123" };
      mockModels.UtteranceTranslation.findAll.mockResolvedValue(mockTranslations);

      await controller.getTranslationsByUtteranceId(mockReq as any, mockRes as Response);

      expect(successResponse).toHaveBeenCalledWith(mockTranslations);
    });

    it("should return 404 if no translations found", async () => {
      mockReq.params = { utteranceId: "non-existent" };
      mockModels.UtteranceTranslation.findAll.mockResolvedValue([]);

      await controller.getTranslationsByUtteranceId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle error", async () => {
      mockReq.params = { utteranceId: "utterance-123" };
      mockModels.UtteranceTranslation.findAll.mockRejectedValue(new Error("Database error"));

      await controller.getTranslationsByUtteranceId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("getTranslationByUtteranceIdAndLangId", () => {
    it("should retrieve translation by utterance and language ID", async () => {
      const mockTranslation = { id: "trans-123" };
      mockReq.params = { utteranceId: "utterance-1", langId: "lang-1" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue(mockTranslation);

      await controller.getTranslationByUtteranceIdAndLangId(mockReq as any, mockRes as Response);

      expect(successResponse).toHaveBeenCalledWith(mockTranslation);
    });

    it("should return 404 if no translation found", async () => {
      mockReq.params = { utteranceId: "utterance-1", langId: "lang-non-existent" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue(null);

      await controller.getTranslationByUtteranceIdAndLangId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle error", async () => {
      mockReq.params = { utteranceId: "utterance-1", langId: "lang-1" };
      mockModels.UtteranceTranslation.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getTranslationByUtteranceIdAndLangId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("update", () => {
    it("should update utterance translation successfully", async () => {
      const mockUpdatedTranslation = { id: "trans-123", text: "Updated Text" };
      mockReq.params = { id: "trans-123" };
      mockReq.body = { text: "Updated Text" };
      mockModels.UtteranceTranslation.update.mockResolvedValue([1]);
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue(mockUpdatedTranslation);

      await controller.update(mockReq as any, mockRes as Response);

      expect(successResponse).toHaveBeenCalledWith(mockUpdatedTranslation);
    });

    it("should return 404 if translation not found during update", async () => {
      mockReq.params = { id: "non-existent" };
      mockReq.body = { text: "Updated Text" };
      mockModels.UtteranceTranslation.update.mockResolvedValue([0]);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle update error", async () => {
      mockReq.params = { id: "trans-123" };
      mockReq.body = { text: "Updated Text" };
      mockModels.UtteranceTranslation.update.mockRejectedValue(new Error("Update failed"));

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });

    it("should handle missing user in update request", async () => {
      const mockUpdatedTranslation = { id: "trans-123", text: "Updated Text" };
      mockReq.params = { id: "trans-123" };
      mockReq.body = { text: "Updated Text" };
      mockReq.user = { id: undefined as any };
      mockModels.UtteranceTranslation.update.mockResolvedValue([1]);
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue(mockUpdatedTranslation);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockModels.UtteranceTranslation.update).toHaveBeenCalledWith(
        { text: "Updated Text", updatedBy: undefined },
        { where: { id: "trans-123" } },
      );
    });
  });

  describe("delete", () => {
    it("should delete translation and parent utterance if no other translations", async () => {
      const mockTranslation = { id: "trans-123", utteranceId: "utterance-1" };
      mockReq.params = { id: "trans-123" };
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.UtteranceTranslation.destroy.mockResolvedValue(1);
      mockModels.UtteranceTranslation.count.mockResolvedValue(0);
      mockModels.IntentUtterance.destroy.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(204);
    });

    it("should delete only translation if other translations exist", async () => {
      const mockTranslation = { id: "trans-123", utteranceId: "utterance-1" };
      mockReq.params = { id: "trans-123" };
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.UtteranceTranslation.destroy.mockResolvedValue(1);
      mockModels.UtteranceTranslation.count.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(204);
    });

    it("should return 404 if destroy returns 0", async () => {
      const mockTranslation = { id: "trans-123", utteranceId: "utterance-1" };
      mockReq.params = { id: "trans-123" };
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.UtteranceTranslation.destroy.mockResolvedValue(0);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should return 404 if translation not found", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue(null);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "trans-123" };
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue({
        id: "trans-123",
        utteranceId: "utterance-1",
      });
      mockModels.UtteranceTranslation.destroy.mockRejectedValue(new Error("Delete failed"));

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });
});
