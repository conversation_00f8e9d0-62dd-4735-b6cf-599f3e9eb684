// Mock all external dependencies before importing the main module
jest.mock("dotenv", () => ({
  config: jest.fn(),
}));

jest.mock("api_gw", () => ({
  initializeApiGw: jest.fn().mockResolvedValue(undefined),
}));

jest.mock("../app", () => ({
  App: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockResolvedValue(undefined),
    stop: jest.fn().mockResolvedValue(undefined),
  })),
}));

jest.mock("@neuratalk/common", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock process methods
const mockExit = jest.spyOn(process, "exit").mockImplementation(() => {
  throw new Error("process.exit() was called");
});

const mockOn = jest.spyOn(process, "on").mockImplementation(() => process);

describe("Index (Main Entry Point)", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset modules to ensure fresh import
    jest.resetModules();
  });

  afterEach(() => {
    mockExit.mockClear();
    mockOn.mockClear();
  });

  it("should import and execute main function without errors", async () => {
    // Import the main module to trigger execution
    await import("../index");
    
    // Verify that the main dependencies were called
    const { initializeApiGw } = require("api_gw");
    const { App } = require("../app");
    
    expect(initializeApiGw).toHaveBeenCalled();
    expect(App).toHaveBeenCalled();
  });

  it("should register process event handlers", async () => {
    await import("../index");
    
    // Verify that process event handlers were registered
    expect(mockOn).toHaveBeenCalledWith("SIGTERM", expect.any(Function));
    expect(mockOn).toHaveBeenCalledWith("SIGINT", expect.any(Function));
  });
});
