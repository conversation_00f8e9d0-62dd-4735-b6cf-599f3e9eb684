import { Request, Response } from "express";
import { FaqItemsController } from "../controllers/faq-items.controller";

jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  successResponse: jest.fn((data) => ({
    success: true,
    data,
    timestamp: new Date(),
  })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error,
    timestamp: new Date(),
  })),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

const { getPaginatedResults, successResponse, errorResponse } = require("@neuratalk/common");

describe("FaqItemsController", () => {
  let controller: FaqItemsController;
  let mockModels: any;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;

  beforeEach(() => {
    mockModels = {
      FaqItems: {
        create: jest.fn(),
        findOne: jest.fn(),
        findByPk: jest.fn(),
        update: jest.fn(),
        destroy: jest.fn(),
      },
    };

    controller = new FaqItemsController({
      db: {
        models: mockModels,
      },
    } as any);

    mockReq = {
      body: {},
      params: {},
      query: {},
      user: { id: "user-123" },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create FAQ item successfully", async () => {
      const mockFaqItem = { id: "faq-123", questions: ["Q1"], answer: "A1" };
      mockReq.body = { botId: "bot-123", flowId: "flow-123", categoryId: "category-123" };
      mockModels.FaqItems.create.mockResolvedValue(mockFaqItem);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockModels.FaqItems.create).toHaveBeenCalledWith({
        botId: "bot-123",
        flowId: "flow-123",
        categoryId: "category-123",
        createdBy: "user-123",
        updatedBy: "user-123",
      });
      expect(mockRes.status).toHaveBeenCalledWith(201);
    });

    it("should handle creation error", async () => {
      mockReq.body = { botId: "bot-123", flowId: "flow-123", categoryId: "category-123" };
      mockModels.FaqItems.create.mockRejectedValue(new Error("Creation failed"));

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });

  describe("getAll", () => {
    it("should get paginated FAQ items", async () => {
      const mockResult = {
        items: [{ id: "faq-1" }, { id: "faq-2" }],
        pagination: { page: 1, limit: 20, total: 2, totalPages: 1, hasNext: false, hasPrev: false },
      };
      mockReq.query = {
        page: "1",
        limit: "20",
        filter: JSON.stringify({ botId: { eq: "bot-123" } }),
      };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(getPaginatedResults).toHaveBeenCalledWith(mockModels.FaqItems, mockReq.query, [
        "answer",
      ]);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockResult,
        timestamp: expect.any(Date),
      });
    });

    it("should handle getAll error", async () => {
      mockReq.query = { page: "1", limit: "20" };
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("getById", () => {
    it("should get FAQ item by id", async () => {
      const mockFaqItem = { id: "faq-123", questions: ["Q1"], answer: "A1" };
      mockReq.params = { id: "faq-123" };
      mockModels.FaqItems.findOne.mockResolvedValue(mockFaqItem);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockModels.FaqItems.findOne).toHaveBeenCalledWith({
        where: { id: "faq-123" },
      });
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockFaqItem,
        timestamp: expect.any(Date),
      });
    });

    it("should return 404 when FAQ item not found", async () => {
      mockReq.params = { id: "faq-123" };
      mockModels.FaqItems.findOne.mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle getById error", async () => {
      mockReq.params = { id: "faq-123" };
      mockModels.FaqItems.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("update", () => {
    it("should update FAQ item successfully", async () => {
      const mockUpdatedFaq = {
        id: "faq-123",
        botId: "bot-123",
        flowId: "flow-123",
        categoryId: "category-123",
      };
      mockReq.params = { id: "faq-123" };
      mockReq.body = { botId: "bot-123", flowId: "flow-123", categoryId: "category-123" };
      mockModels.FaqItems.update.mockResolvedValue([1]);
      mockModels.FaqItems.findByPk.mockResolvedValue(mockUpdatedFaq);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockModels.FaqItems.update).toHaveBeenCalledWith(
        { botId: "bot-123", flowId: "flow-123", categoryId: "category-123", updatedBy: "user-123" },
        { where: { id: "faq-123" } },
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockUpdatedFaq,
        timestamp: expect.any(Date),
      });
    });

    it("should return 404 when updating non-existent FAQ item", async () => {
      mockReq.params = { id: "faq-123" };
      mockReq.body = { answer: "Updated A" };
      mockModels.FaqItems.update.mockResolvedValue([0]);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle update error", async () => {
      mockReq.params = { id: "faq-123" };
      mockReq.body = { answer: "Updated A" };
      mockModels.FaqItems.update.mockRejectedValue(new Error("Update failed"));

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });

  describe("delete", () => {
    it("should delete FAQ item successfully", async () => {
      mockReq.params = { id: "faq-123" };
      mockModels.FaqItems.destroy.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockModels.FaqItems.destroy).toHaveBeenCalledWith({
        where: { id: "faq-123" },
      });
      expect(mockRes.status).toHaveBeenCalledWith(204);
    });

    it("should return 404 when deleting non-existent FAQ item", async () => {
      mockReq.params = { id: "faq-123" };
      mockModels.FaqItems.destroy.mockResolvedValue(0);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "faq-123" };
      mockModels.FaqItems.destroy.mockRejectedValue(new Error("Delete failed"));

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });
});
