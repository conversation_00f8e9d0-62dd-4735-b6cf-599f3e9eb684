import { Request, Response, NextFunction } from "express";
import { DatabaseConnection, PlatformConfigKey } from "@neuratalk/bot-store";
import { errorResponse } from "@neuratalk/common";
import { AppContext } from "../types/context.types";

export const validateFaqQuestions = (context: AppContext) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const { questions } = req.body;

    if (!questions || !Array.isArray(questions)) {
      return res.status(400).json(
        errorResponse({
          code: "VALIDATION_ERROR",
          message: "'questions' array is required.",
        }),
      );
    }

    const db: DatabaseConnection = context.db;
    const config = await db.models.PlatformConfig.findOne({
      where: { key: PlatformConfigKey.FAQ_QUESTION_LIMIT },
    });
    const faqQuestionLimit = config ? (config.value as number) : 10; // Default to 10 if not found

    if (questions.length > faqQuestionLimit) {
      return res.status(400).json(
        errorResponse({
          code: "VALIDATION_ERROR",
          message: `A maximum of ${faqQuestionLimit} questions can be only be added to a FAQ item.`,
        }),
      );
    }

    return next();
  };
};
