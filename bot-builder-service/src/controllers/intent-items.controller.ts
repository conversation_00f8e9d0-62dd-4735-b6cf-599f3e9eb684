import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import {
  CreateIntentItemRequest,
  UpdateIntentItemRequest,
  AssignFlowToIntentRequest,
} from "../schemas";
import { logger } from "@neuratalk/common";
import { AppContext } from "../types/context.types";

export class IntentItemsController {
  private models: Models;
  private context: AppContext;

  constructor(context: AppContext) {
    this.models = context.db.models;
    this.context = context;
  }

  /**
   * @swagger
   * /api/v1/intent-items:
   *   post:
   *     summary: Create intent item
   *     tags: [Intent Items]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - botId
   *               - name
   *             properties:
   *               botId:
   *                 type: string
   *                 format: uuid
   *               name:
   *                 type: string
   *               description:
   *                 type: string
   *     responses:
   *       201:
   *         description: Created
   */
  public create = async (
    req: Request<any, any, CreateIntentItemRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const intentItem = await this.models.IntentItems.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`Intent item created: ${intentItem.id}`);
      res.status(201).json(successResponse(intentItem));
    } catch (error) {
      logger.error("Error creating intent item:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/intent-items:
   *   get:
   *     summary: List intent items with filtering and pagination
   *     tags: [Intent Items]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *       - in: query
   *         name: filter
   *         schema:
   *           type: string
   *         example: '{"botId":{"eq":"uuid"}}'
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentUtterances,entities)
   *     responses:
   *       200:
   *         description: Success
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.IntentItems, req.query, ["name"]);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching intent items:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch intent items",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/intent-items/{id}:
   *   get:
   *     summary: Get intent item by ID
   *     tags: [Intent Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentUtterances,entities)
   *     responses:
   *       200:
   *         description: Success
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const intentItem = await this.models.IntentItems.findOne({
        where: { id },
      });

      if (!intentItem) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Intent item not found" }));
        return;
      }

      res.json(successResponse(intentItem));
    } catch (error) {
      logger.error(`Error fetching intent item ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch intent item",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/intent-items/{id}:
   *   put:
   *     summary: Update intent item
   *     tags: [Intent Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentUtterances,entities)
   *     responses:
   *       200:
   *         description: Updated
   */
  public update = async (
    req: Request<UuidParams, any, UpdateIntentItemRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const [updated] = await this.models.IntentItems.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id },
        },
      );

      if (!updated) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Intent item not found" }));
        return;
      }

      const intentItem = await this.models.IntentItems.findByPk(id);
      logger.info(`Intent item updated: ${id}`);

      res.json(successResponse(intentItem));
    } catch (error) {
      logger.error(`Error updating intent item ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/intent-items/{id}:
   *   delete:
   *     summary: Delete intent item
   *     tags: [Intent Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Deleted
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.IntentItems.destroy({ where: { id } });

      if (!deleted) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Intent item not found" }));
        return;
      }

      logger.info(`Intent item deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting intent item ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to delete intent item",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/intent-items/assign-flow:
   *   post:
   *     summary: Assign a flow to an intent
   *     tags: [Intent Items]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - flowId
   *               - intentId
   *             properties:
   *               flowId:
   *                 type: string
   *                 format: uuid
   *                 description: The ID of the flow to assign.
   *               intentId:
   *                 type: string
   *                 format: uuid
   *                 description: The ID of the intent to assign the flow to.
   *     responses:
   *       200:
   *         description: Flow assigned to intent successfully.
   *       400:
   *         description: Invalid request data or intent/flow not found.
   *       500:
   *         description: Internal server error.
   */
  public assignFlowToIntent = async (
    req: Request<any, any, AssignFlowToIntentRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const { flowId, intentId } = req.body;

      await this.context.db.transaction(async (t) => {
        const targetIntent = await this.models.IntentItems.findByPk(intentId, { transaction: t });
        if (!targetIntent) {
          throw new Error(`Intent with ID ${intentId} not found.`);
        }

        if (targetIntent.flowId === flowId) {
          logger.info(`Flow ${flowId} already assigned to intent ${intentId}`);
          return;
        }

        await this.models.IntentItems.update(
          { flowId: null, updatedBy: userId },
          {
            where: {
              flowId,
              botId: targetIntent.botId,
            },
            transaction: t,
          },
        );
        logger.info(`Detached flow ${flowId} from other intents for bot ${targetIntent.botId}`);

        // 3. Assign flowId to the target intent
        await targetIntent.update({ flowId, updatedBy: userId }, { transaction: t });
        logger.info(`Assigned flow ${flowId} to intent ${intentId}`);
      });

      res.status(200).json(successResponse({ message: "Flow assigned successfully" }));
    } catch (error) {
      logger.error("Error assigning flow to intent:", error);
      res.status(400).json(
        errorResponse({
          error,
          code: "ASSIGN_FLOW_ERROR",
          message: (error as Error).message,
        }),
      );
    }
  };
}
