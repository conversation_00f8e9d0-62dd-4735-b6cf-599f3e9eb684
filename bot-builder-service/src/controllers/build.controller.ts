import { Request, Response } from 'express';
import { BuildService } from '../services/build.service';
import { logger } from '@neuratalk/common';

export class BuildController {
  private buildService: BuildService;
  
  constructor(buildService: BuildService) {
    this.buildService = buildService;
  }

  async createBuild(req: Request, res: Response): Promise<void> {
    try {
      const { botId } = req.params;
      const { jobId } = await this.buildService.createBuild(botId);
      res.status(202).json({ 
        success: true,
        data: {
          message: 'Build job successfully queued.',
          jobId: jobId,
          botId: botId
        },
        timestamp: new Date()
      });
    } catch (error) {
      logger.error('Error starting build process:', error);
      res.status(500).json({ message: 'Error starting build process.' });
    }
  }
}