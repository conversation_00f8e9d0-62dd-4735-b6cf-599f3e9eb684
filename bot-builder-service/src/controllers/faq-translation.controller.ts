import { Request, Response } from "express";
import { DatabaseConnection, FaqTranslationModel, Language, Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  UuidParams,
  successResponse,
  errorResponse,
  PaginationQuery,
} from "@neuratalk/common";
import {
  CreateFaqTranslationRequest,
  FaqIdParam,
  FaqsByCategoryAndLanguageParam,
  FaqTranslationByLangParam,
  UpdateFaqTranslationRequest,
} from "../schemas";
import { logger } from "@neuratalk/common";
import { AppContext } from "../types/context.types";
import { Op } from "sequelize";

export class FaqTranslationController {
  private models: Models;
  private db: DatabaseConnection;

  constructor(private context: AppContext) {
    this.models = context.db.models;
    this.db = context.db;
  }

  /**
   * @swagger
   * /api/v1/faq-translations:
   *   post:
   *     summary: Create a new FAQ item with translations
   *     tags: [FAQ Translations]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required: [categoryId, botId, translation]
   *             properties:
   *               faqId:
   *                 type: string
   *                 format: uuid
   *                 description: Optional. If provided, translations will be added to this existing FAQ item.
   *               categoryId:
   *                 type: string
   *                 format: uuid
   *                 description: The category ID for the FAQ item.
   *               botId:
   *                 type: string
   *                 format: uuid
   *                 description: The bot ID for the FAQ item.
   *               flowId:
   *                 type: string
   *                 format: uuid
   *                 nullable: true
   *                 description: Optional. The flow ID for the FAQ item.
   *               translation:
   *                 type: object
   *                 required: [langId, questions, answer]
   *                 properties:
   *                   id:
   *                     type: string
   *                     format: uuid
   *                     description: Optional. If provided, updates an existing translation.
   *                   langId:
   *                     type: string
   *                     format: uuid
   *                   questions:
   *                     type: array
   *                     items:
   *                       type: string
   *                   answer:
   *                     type: string
   *                   metadata:
   *                     type: object
   *                     additionalProperties: true
   *     responses:
   *       201:
   *         description: FAQ item and translations created/updated successfully
   *       400:
   *         description: Invalid request data
   *       500:
   *         description: Internal server error
   */
  public createFaqTranslation = async (
    req: Request<any, any, CreateFaqTranslationRequest>,
    res: Response,
  ): Promise<void> => {
    const { faqId, categoryId, botId, flowId, ...translationData } = req.body;
    const userId = req.user.id;

    try {
      const result = await this.db.transaction(async (transaction) => {
        let faqItem;

        if (faqId) {
          faqItem = await this.models.FaqItems.findByPk(faqId, { transaction });
          if (!faqItem) {
            throw new Error(`FAQ Item with ID ${faqId} not found.`);
          }

          const existingTranslation = await this.models.FaqTranslation.findOne({
            where: { faqId, langId: translationData.langId },
            transaction,
          });

          if (existingTranslation) {
            throw new Error(
              `FAQ translation for langId ${translationData.langId} already exists for FAQ item ${faqId}.`,
            );
          }
        } else {
          faqItem = await this.models.FaqItems.create(
            {
              categoryId,
              botId,
              flowId,
              createdBy: userId,
              updatedBy: userId,
            },
            { transaction },
          );
        }

        const newTranslation = await this.models.FaqTranslation.create(
          {
            ...translationData,
            faqId: faqItem.id,
            createdBy: userId,
            updatedBy: userId,
          },
          { transaction },
        );

        return { ...faqItem.toJSON(), translation: newTranslation.toJSON() };
      });

      res.status(201).json(successResponse(result));
    } catch (error) {
      logger.error("Error creating FAQ translation:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-translations:
   *   get:
   *     summary: Get all FAQs by category and language
   *     tags: [FAQ Translations]
   *     parameters:
   *       - in: query
   *         name: categoryId
   *         schema:
   *           type: string
   *           format: uuid
   *         required: true
   *         description: Filter FAQs by category ID.
   *       - in: query
   *         name: langId
   *         schema:
   *           type: string
   *           format: uuid
   *         required: true
   *         description: Filter FAQs by language ID for translations.
   *       - in: query
   *         name: page
   *         schema: { type: integer, default: 1 }
   *       - in: query
   *         name: limit
   *         schema: { type: integer, default: 20 }
   *       - in: query
   *         name: search
   *         schema: { type: string }
   *         description: Search term for questions or answers in the specified language.
   *     responses:
   *       200:
   *         description: List of FAQ items with translations and available languages.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success: { type: boolean, example: true }
   *                 data:
   *                   type: object
   *                   properties:
   *                     items:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id: { type: string, format: uuid }
   *                           categoryId: { type: string, format: uuid }
   *                           botId: { type: string, format: uuid }
   *                           flowId: { type: string, format: uuid, nullable: true }
   *                           translation:
   *                             type: object
   *                             properties:
   *                               id: { type: string, format: uuid }
   *                               questions: { type: array, items: { type: string } }
   *                               answer: { type: string }
   *                               langId: { type: string, format: uuid }
   *                           availableLanguages:
   *                             type: array
   *                             items:
   *                               type: object
   *                               properties:
   *                                 langId: { type: string, format: uuid }
   *                                 name: { type: string }
   *                                 code: { type: string }
   *                     pagination:
   *                       $ref: '#/components/schemas/Pagination'
   *                 timestamp: { type: string, format: date-time }
   *       400:
   *         description: Invalid query parameters
   *       500:
   *         description: Internal server error
   */
  public getFaqsByCategoryAndLanguage = async (
    req: Request<FaqsByCategoryAndLanguageParam, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { langId, categoryId } = req.params;
      const paginationQuery = req.query;

      const includeAssociations = [
        {
          model: this.models.FaqTranslation,
          as: "faqTranslations",
          where: { langId },
          required: true,
        },
      ];

      const paginatedResult = await getPaginatedResults(
        this.models.FaqItems,
        {
          ...paginationQuery,
          filter: {
            ...paginationQuery.filter,
            categoryId: { eq: categoryId },
          },
        } as PaginationQuery,
        ["faqTranslations.questions", "faqTranslations.answer"],
        includeAssociations,
      );

      const result = {
        ...paginatedResult,
        items: paginatedResult.items.map((item) => ({
          ...item?.faqTranslations?.[0]?.toJSON(),
          flowId: item.flowId,
          botId: item.botId,
          categoryId: item.categoryId,
          faqId: item.id,
        })),
      };

      const faqIds = result.items.map((item) => item.faqId);
      const translations = await this.models.FaqTranslation.findAll({
        where: {
          faqId: { [Op.in]: faqIds },
        },
        include: [
          {
            model: this.models.Language,
            as: "language",
            attributes: ["id", "name", "code"],
          },
        ],
      });

      const translationsByFaqId = translations.reduce(
        (acc, t) => {
          if (!acc[t.faqId]) {
            acc[t.faqId] = [];
          }
          acc[t.faqId].push(t);
          return acc;
        },
        {} as Record<string, FaqTranslationModel[]>,
      );

      const formattedFaqs = result.items.map((faqTranslation) => {
        const availableLanguages = (translationsByFaqId[faqTranslation.faqId] || []).reduce<
          Partial<Language>[]
        >((acc, t): Partial<Language>[] => {
          if (t.langId !== faqTranslation.langId) {
            acc.push({
              id: t.langId,
              name: t.language?.name,
              code: t.language?.code,
            });
          }
          return acc;
        }, []);

        return {
          ...faqTranslation,
          availableLanguages,
        };
      });

      res.json(
        successResponse({
          ...result,
          items: formattedFaqs,
        }),
      );
    } catch (error) {
      logger.error("Error fetching FAQs by category and language:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch FAQs." }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-translations/{id}:
   *   get:
   *     summary: Get FAQ translation by ID
   *     tags: [FAQ Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: FAQ translation object
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const faqTranslation = await this.models.FaqTranslation.findOne({
        where: { id },
      });

      if (!faqTranslation) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ translation not found" }));
        return;
      }

      res.json(successResponse(faqTranslation));
    } catch (error) {
      logger.error(`Error fetching FAQ translation ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch FAQ translation",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/faq/{faqId}/translations:
   *   get:
   *     summary: Get all translations for a given FAQ ID
   *     tags: [FAQ Translations]
   *     parameters:
   *       - in: path
   *         name: faqId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: List of FAQ translations for the given FAQ ID
   */
  public getTranslationsByFaqId = async (
    req: Request<FaqIdParam, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { faqId } = req.params;

      req.query = {
        ...req.query,
        filter: {
          ...req.query.filter,
          faqId: { eq: faqId },
        },
      };

      const translations = await getPaginatedResults(this.models.FaqTranslation, req.query, [
        "answer",
        "questions",
      ]);

      if (translations.items.length === 0) {
        res
          .status(404)
          .json(
            errorResponse({ code: "NOT_FOUND", message: "No translations found for this FAQ ID" }),
          );
        return;
      }

      res.json(successResponse(translations));
    } catch (error) {
      logger.error(`Error fetching translations for FAQ ${req.params.faqId}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch FAQ translations",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/faq/{faqId}/lang/{langId}/translation:
   *   get:
   *     summary: Get a specific translation for a given FAQ ID and language ID
   *     tags: [FAQ Translations]
   *     parameters:
   *       - in: path
   *         name: faqId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: path
   *         name: langId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: FAQ translation object for the given FAQ ID and language ID
   */
  public getTranslationByFaqIdAndLangId = async (
    req: Request<FaqTranslationByLangParam, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { faqId, langId } = req.params;

      const translation = await this.models.FaqTranslation.findOne({
        where: { faqId, langId },
      });

      if (!translation) {
        res.status(404).json(
          errorResponse({
            code: "NOT_FOUND",
            message: "No translation found for this FAQ ID and language ID",
          }),
        );
        return;
      }

      res.json(successResponse(translation));
    } catch (error) {
      logger.error(
        `Error fetching translation for FAQ ${req.params.faqId} and language ${req.params.langId}:`,
        error,
      );
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch FAQ translation",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/faq-translations/{id}:
   *   put:
   *     summary: Update an FAQ translation
   *     tags: [FAQ Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/FaqTranslation'
   *     responses:
   *       200:
   *         description: FAQ translation updated successfully
   */
  public updateFaqTranslation = async (
    req: Request<UuidParams, any, UpdateFaqTranslationRequest & { flowId?: string }>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      const { flowId, ...updateData } = req.body;

      const faqTranslation = await this.models.FaqTranslation.findByPk(id);
      if (!faqTranslation) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ translation not found" }));
        return;
      }

      await this.db.transaction(async (transaction) => {
        await this.models.FaqTranslation.update(
          {
            ...updateData,
            updatedBy: userId,
          },
          {
            where: { id },
            transaction,
          },
        );

        if (flowId !== undefined) {
          await this.models.FaqItems.update(
            { flowId, updatedBy: userId },
            {
              where: { id: faqTranslation.faqId },
              transaction,
            },
          );
        }
      });

      const updatedFaqTranslation = await this.models.FaqTranslation.findByPk(id, {
        include: [{ model: this.models.FaqItems, as: "faqItem" }],
      });
      logger.info(`FAQ translation updated: ${id}`);

      res.json(successResponse(updatedFaqTranslation));
    } catch (error) {
      logger.error(`Error updating FAQ translation ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-translations/{id}:
   *   delete:
   *     summary: Delete an FAQ translation
   *     tags: [FAQ Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: FAQ translation deleted successfully
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const translationToDelete = await this.models.FaqTranslation.findByPk(id);
      if (!translationToDelete) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ translation not found" }));
        return;
      }

      const faqId = translationToDelete.faqId;

      const deleted = await this.models.FaqTranslation.destroy({ where: { id } });

      if (!deleted) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ translation not found" }));
        return;
      }

      logger.info(`FAQ translation deleted: ${id}`);

      // Check if this was the last translation for its parent FAQ item
      const remainingTranslations = await this.models.FaqTranslation.count({
        where: { faqId },
      });

      if (remainingTranslations === 0) {
        // If no more translations, delete the parent FaqItem
        await this.models.FaqItems.destroy({ where: { id: faqId } });
        logger.info(`Parent FaqItem group deleted as it has no more translations: ${faqId}`);
      }

      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting FAQ translation ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to delete FAQ translation",
        }),
      );
    }
  };
}
