import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import { CreateLanguageRequest, UpdateLanguageRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { WhereOptions } from "sequelize";
import { AppContext } from "../types/context.types";

export class LanguageController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/languages:
   *   post:
   *     summary: Create a new language
   *     tags: [Languages]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Language'
   *     responses:
   *       201:
   *         description: Language created successfully
   */
  public create = async (
    req: Request<any, any, CreateLanguageRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const language = await this.models.Language.create({
        ...req.body,
      });

      logger.info(`Language created: ${language.id}`);
      res.status(201).json(successResponse(language));
    } catch (error) {
      logger.error("Error creating language:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/languages:
   *   get:
   *     summary: Get all languages
   *     tags: [Languages]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema: { type: integer }
   *       - in: query
   *         name: limit
   *         schema: { type: integer }
   *     responses:
   *       200:
   *         description: List of languages
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.Language, req.query, ["name", "code"]);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching languages:", error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch languages" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/languages/{id}:
   *   get:
   *     summary: Get language by ID
   *     tags: [Languages]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Language object
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const language = await this.models.Language.findOne({
        where: { id },
      });

      if (!language) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "Language not found" }));
        return;
      }

      res.json(successResponse(language));
    } catch (error) {
      logger.error(`Error fetching language ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch language" }),
        );
    }
  };

  
}
