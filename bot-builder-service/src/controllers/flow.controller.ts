/**
 * Flow Controller
 *
 * Handles HTTP requests for flow management operations.
 *
 * @swagger
 * components:
 *   schemas:
 *     FlowResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ApiResponse'
 *         - type: object
 *           properties:
 *             data:
 *               $ref: '#/components/schemas/Flow'
 *     FlowsResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ApiResponse'
 *         - type: object
 *           properties:
 *             data:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginationResponse'
 *                 - type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Flow'
 */

import { Request, Response } from "express";
import { FlowService } from "../services/flow.service";
import {
  CreateFlowRequest,
  UpdateFlowRequest,
  CreateFlowResponse,
  GetFlowsResponse,
} from "../types";
import { getTotalPages } from "../utils/api";
import { getStudioAppsService } from "api_gw";
import { logger, successResponse, errorResponse } from "@neuratalk/common";

export class FlowController {
  private flowService: FlowService;
  private studioAppsService;

  constructor(flowService: FlowService) {
    this.flowService = flowService;
    this.studioAppsService = getStudioAppsService();
  }

  /**
   * @swagger
   * /api/v1/flows:
   *   post:
   *     summary: Create a new flow
   *     description: Creates a new flow for a specific bot
   *     tags: [Flows]
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateFlowRequest'
   *     responses:
   *       201:
   *         description: Flow created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/FlowResponse'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  public createFlow = async (req: Request, res: Response): Promise<void> => {
    try {
      const request: CreateFlowRequest = req.body;
      const botId = (request as any).botId;
      const userId = req.user.id;

      const appInfo = await this.studioAppsService.createAppInfo(req, res);
      if (res.headersSent) {
        return;
      }

      const flow = await this.flowService.createFlow(request, botId, appInfo.appId, userId);

      const response: CreateFlowResponse = { flow };

      res.status(201).json(successResponse(response));
    } catch (error) {
      logger.error("Error in createFlow controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to create flow" }));
    }
  };

  /**
   * @swagger
   * /api/v1/flows/{id}:
   *   get:
   *     summary: Get flow by ID
   *     description: Retrieves a specific flow by its unique identifier
   *     tags: [Flows]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Flow unique identifier
   *     responses:
   *       200:
   *         description: Flow retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/FlowResponse'
   *       404:
   *         description: Flow not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  public getFlowById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const flow = await this.flowService.getFlowById(id);

      if (!flow) {
        res.status(404).json(errorResponse({ code: "FLOW_NOT_FOUND", message: "Flow not found" }));
        return;
      }

      res.json(successResponse(flow));
    } catch (error) {
      logger.error("Error in getFlowById controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to get flow" }));
    }
  };

  /**
   * @swagger
   * /api/v1/flows/{id}:
   *   put:
   *     summary: Update flow
   *     description: Updates an existing flow
   *     tags: [Flows]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Flow unique identifier
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateFlowRequest'
   *     responses:
   *       200:
   *         description: Flow updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/FlowResponse'
   *       404:
   *         description: Flow not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  public updateFlow = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const request: UpdateFlowRequest = req.body;
      const userId = (req as any).user?.id;

      const flow = await this.flowService.updateFlow(id, request, userId);

      if (!flow) {
        res.status(404).json(errorResponse({ code: "FLOW_NOT_FOUND", message: "Flow not found" }));
        return;
      }

      res.json(successResponse(flow));
    } catch (error) {
      logger.error("Error in updateFlow controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to update flow" }));
    }
  };

  /**
   * @swagger
   * /api/v1/flows/{id}:
   *   delete:
   *     summary: Delete flow
   *     description: Deletes a specific flow
   *     tags: [Flows]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Flow unique identifier
   *     responses:
   *       204:
   *         description: Flow deleted successfully
   *       404:
   *         description: Flow not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  public deleteFlow = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const deletedAppInfo = await this.studioAppsService.purgeAppInfo(req, res);
      if (res.headersSent) {
        return;
      }

      const deleted = await this.flowService.deleteFlow(id);

      if (!deleted) {
        res.status(404).json(errorResponse({ code: "FLOW_NOT_FOUND", message: "Flow not found" }));
        return;
      }

      res.status(204).send();
    } catch (error) {
      logger.error("Error in deleteFlow controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to delete flow" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{botId}/flows:
   *   get:
   *     summary: Get flows by bot ID
   *     description: Retrieves all flows for a specific bot with pagination and filtering
   *     tags: [Flows]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: Page number
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 20
   *         description: Items per page
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search term for flow name or description
   *       - in: query
   *         name: isActive
   *         schema:
   *           type: boolean
   *         description: Filter by active status
   *     responses:
   *       200:
   *         description: Flows retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/FlowsResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  public getFlowsByBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { botId } = req.params;
      const { page = 1, limit = 20, search, isActive } = req.query as any;

      const { flows, total } = await this.flowService.getFlowsByBot(
        botId,
        parseInt(page, 10),
        parseInt(limit, 10),
        search,
        isActive === "true" ? true : isActive === "false" ? false : undefined,
      );

      const totalPages = getTotalPages(total, parseInt(limit, 10));

      const response: GetFlowsResponse = {
        items: flows,
        pagination: {
          page: parseInt(page, 10),
          limit: parseInt(limit, 10),
          total,
          totalPages,
          hasNext: parseInt(page, 10) < totalPages,
          hasPrev: parseInt(page, 10) > 1,
        },
      };

      res.json(successResponse(response));
    } catch (error) {
      logger.error("Error in getFlowsByBot controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to get flows" }));
    }
  };
}
