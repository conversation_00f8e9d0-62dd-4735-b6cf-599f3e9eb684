import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import { CreateFaqItemRequest, UpdateFaqItemRequest } from "../schemas/faq.schemas";
import { logger } from "@neuratalk/common";
import { AppContext } from "../types/context.types";

export class FaqItemsController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/faq-items:
   *   post:
   *     summary: Create a new FAQ item
   *     tags: [FAQ Items]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateFaqItemRequest'
   *     responses:
   *       201:
   *         description: FAQ item created successfully
   */
  public create = async (
    req: Request<any, any, CreateFaqItemRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const faqItem = await this.models.FaqItems.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`FAQ item created: ${faqItem.id}`);
      res.status(201).json(successResponse(faqItem));
    } catch (error) {
      logger.error("Error creating FAQ item:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-items:
   *   get:
   *     summary: Get all FAQ items
   *     tags: [FAQ Items]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema: { type: integer }
   *       - in: query
   *         name: limit
   *         schema: { type: integer }
   *       - in: query
   *         name: botId
   *         schema: { type: string, format: uuid }
   *       - in: query
   *         name: categoryId
   *         schema: { type: string, format: uuid }
   *     responses:
   *       200:
   *         description: List of FAQ items
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.FaqItems, req.query, ["answer"]);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching FAQ items:", error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch FAQ items" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/faq-items/{id}:
   *   get:
   *     summary: Get FAQ item by ID
   *     tags: [FAQ Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: FAQ item object
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const faqItem = await this.models.FaqItems.findOne({
        where: { id },
      });

      if (!faqItem) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "FAQ item not found" }));
        return;
      }

      res.json(successResponse(faqItem));
    } catch (error) {
      logger.error(`Error fetching FAQ item ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch FAQ item",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/faq-items/{id}:
   *   put:
   *     summary: Update an FAQ item
   *     tags: [FAQ Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateFaqItemRequest'
   *     responses:
   *       200:
   *         description: FAQ item updated successfully
   */
  public update = async (
    req: Request<UuidParams, any, UpdateFaqItemRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const [updated] = await this.models.FaqItems.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id },
        },
      );

      if (!updated) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "FAQ item not found" }));
        return;
      }

      const faqItem = await this.models.FaqItems.findByPk(id);
      logger.info(`FAQ item updated: ${id}`);

      res.json(successResponse(faqItem));
    } catch (error) {
      logger.error(`Error updating FAQ item ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-items/{id}:
   *   delete:
   *     summary: Delete an FAQ item
   *     tags: [FAQ Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: FAQ item deleted successfully
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.FaqItems.destroy({ where: { id } });

      if (!deleted) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "FAQ item not found" }));
        return;
      }

      logger.info(`FAQ item deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting FAQ item ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };
}
