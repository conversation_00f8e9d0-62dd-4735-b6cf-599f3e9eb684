import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
  generateEntityName,
  EntityType,
} from "@neuratalk/common";
import { CreateEntityRequest, UpdateEntityRequest } from "../schemas";
import { logger } from "@neuratalk/common";

import { AppContext } from "../types/context.types";

export class EntitiesController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/entities:
   *   post:
   *     summary: Create entity
   *     tags: [Entities]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - botId
   *               - intentId
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 100
   *               botId:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the bot this entity belongs to
   *               intentId:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the intent item this entity belongs to
   *               metadata:
   *                 type: object
   *                 description: Additional metadata
   *     responses:
   *       201:
   *         description: Created
   */
  public create = async (
    req: Request<any, any, CreateEntityRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const { displayName, entityType, ...entityData } = req.body;

      // Create entity first to get the ID
      const entity = await this.models.Entities.create({
        ...entityData,
        displayName,
        entityType,
        name: "temp", // Temporary name, will be updated
        createdBy: userId,
        updatedBy: userId,
      });

      // Generate the final entity name using the ID
      const finalName = generateEntityName(displayName, entity.id, entityType as EntityType);

      // Update the entity with the final name
      await entity.update({ name: finalName });

      logger.info(`Entity created: ${entity.id} with name: ${finalName}`);
      res.status(201).json(successResponse(entity));
    } catch (error) {
      logger.error("Error creating entity:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/entities:
   *   get:
   *     summary: List entities with filtering and pagination
   *     tags: [Entities]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *       - in: query
   *         name: filter
   *         schema:
   *           type: string
   *         example: '{"name":{"like":"city"}}'
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentItem)
   *     responses:
   *       200:
   *         description: Success
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.Entities, req.query, ["name"]);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching entities:", error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch entities" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/entities/{id}:
   *   get:
   *     summary: Get entity by ID
   *     tags: [Entities]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentItem)
   *     responses:
   *       200:
   *         description: Success
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const entity = await this.models.Entities.findOne({
        where: { id },
      });

      if (!entity) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "Entity not found" }));
        return;
      }

      res.json(successResponse(entity));
    } catch (error) {
      logger.error(`Error fetching entity ${req.params.id}:`, error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch entity" }));
    }
  };

  /**
   * @swagger
   * /api/v1/entities/{id}:
   *   put:
   *     summary: Update entity
   *     tags: [Entities]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentItem)
   *     responses:
   *       200:
   *         description: Updated
   */
  public update = async (
    req: Request<UuidParams, any, UpdateEntityRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const [updated] = await this.models.Entities.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id },
        },
      );

      if (!updated) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "Entity not found" }));
        return;
      }

      const entity = await this.models.Entities.findByPk(id, {});
      logger.info(`Entity updated: ${id}`);

      res.json(successResponse(entity));
    } catch (error) {
      logger.error(`Error updating entity ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/entities/{id}:
   *   delete:
   *     summary: Delete entity
   *     tags: [Entities]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Deleted
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.Entities.destroy({ where: { id } });

      if (!deleted) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "Entity not found" }));
        return;
      }

      logger.info(`Entity deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting entity ${req.params.id}:`, error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to delete entity" }));
    }
  };
}
