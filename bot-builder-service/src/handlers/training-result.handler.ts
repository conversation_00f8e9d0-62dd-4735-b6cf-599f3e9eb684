import { EachMessagePayload } from "kafkajs";
import { logger } from "@neuratalk/common";
import { BuildService } from "../services/build.service";
import { TrainingJobStatus } from "@neuratalk/bot-store";

interface TrainingResultMessage {
  jobId: string;
  botId: string;
  status: TrainingJobStatus;
  modelUrl?: string;
  errorMessage?: string;
}

export class TrainingResultHandler {
  private buildService: BuildService;

  constructor(buildService: BuildService) {
    this.buildService = buildService;
  }

  public handleMessage = async (payload: EachMessagePayload): Promise<void> => {
    try {
      const message: TrainingResultMessage = JSON.parse(payload.message.value!.toString());
      logger.info("Received training result:", { message });

      await this.buildService.updateBuildJob(message.jobId, {
        status: message.status,
        modelUrl: message.modelUrl,
        errorMessage: message.errorMessage,
      });
    } catch (error) {
      logger.error("Error processing training result message:", {
        error,
        value: payload.message.value?.toString(),
      });
    }
  };
}
