import { z } from "zod";
import { UuidSchema } from "@neuratalk/common";

// Entity Annotation schemas
export const CreateEntityAnnotationSchema = z
  .object({
    utteranceTranslationId: UuidSchema,
    entityId: UuidSchema,
    startPosition: z.number().int().min(0),
    endPosition: z.number().int().min(0),
    entityValue: z.string().min(1),
    confidence: z.number().min(0).max(1).optional(),
    metadata: z.record(z.any()).optional(),
  })
  .strict()
  .refine((data) => data.startPosition < data.endPosition, {
    message: "Start position must be less than end position",
    path: ["endPosition"],
  });

export const UpdateEntityAnnotationSchema = CreateEntityAnnotationSchema.omit({
  utteranceTranslationId: true,
})
  .partial()
  .strict();

// Bulk annotation schema for annotating multiple entities in one utterance
export const BulkCreateEntityAnnotationSchema = z
  .object({
    utteranceTranslationId: UuidSchema,
    annotations: z
      .array(
        z.object({
          entityId: UuidSchema,
          startPosition: z.number().int().min(0),
          endPosition: z.number().int().min(0),
          entityValue: z.string().min(1),
          confidence: z.number().min(0).max(1).optional(),
          metadata: z.record(z.any()).optional(),
        }),
      )
      .min(1),
  })
  .strict()
  .refine(
    (data) => {
      // Check that all annotations have valid position ranges
      return data.annotations.every((ann) => ann.startPosition < ann.endPosition);
    },
    {
      message: "All annotations must have start position less than end position",
      path: ["annotations"],
    },
  )
  .refine(
    (data) => {
      // Check for overlapping annotations
      const sortedAnnotations = [...data.annotations].sort(
        (a, b) => a.startPosition - b.startPosition,
      );
      for (let i = 0; i < sortedAnnotations.length - 1; i++) {
        if (sortedAnnotations[i].endPosition > sortedAnnotations[i + 1].startPosition) {
          return false;
        }
      }
      return true;
    },
    {
      message: "Annotations cannot overlap",
      path: ["annotations"],
    },
  );

// Schema for annotating text with entity references
export const AnnotateTextSchema = z
  .object({
    utteranceTranslationId: UuidSchema,
    text: z.string().min(1),
    annotations: z
      .array(
        z.object({
          entityId: UuidSchema,
          startPosition: z.number().int().min(0),
          endPosition: z.number().int().min(0),
          confidence: z.number().min(0).max(1).optional(),
          metadata: z.record(z.any()).optional(),
        }),
      )
      .min(1),
  })
  .strict()
  .refine(
    (data) => {
      // Validate that all positions are within text bounds
      return data.annotations.every(
        (ann) =>
          ann.startPosition >= 0 &&
          ann.endPosition <= data.text.length &&
          ann.startPosition < ann.endPosition,
      );
    },
    {
      message: "All annotation positions must be within text bounds and valid",
      path: ["annotations"],
    },
  )
  .refine(
    (data) => {
      // Check for overlapping annotations
      const sortedAnnotations = [...data.annotations].sort(
        (a, b) => a.startPosition - b.startPosition,
      );
      for (let i = 0; i < sortedAnnotations.length - 1; i++) {
        if (sortedAnnotations[i].endPosition > sortedAnnotations[i + 1].startPosition) {
          return false;
        }
      }
      return true;
    },
    {
      message: "Annotations cannot overlap",
      path: ["annotations"],
    },
  );

// Type extraction
export type CreateEntityAnnotationRequest = z.infer<typeof CreateEntityAnnotationSchema>;
export type UpdateEntityAnnotationRequest = z.infer<typeof UpdateEntityAnnotationSchema>;
export type BulkCreateEntityAnnotationRequest = z.infer<typeof BulkCreateEntityAnnotationSchema>;
export type AnnotateTextRequest = z.infer<typeof AnnotateTextSchema>;
