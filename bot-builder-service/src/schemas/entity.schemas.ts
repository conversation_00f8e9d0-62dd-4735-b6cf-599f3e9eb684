import { z } from "zod";
import { UuidSchema } from "@neuratalk/common";

// Entity schemas
export const CreateEntitySchema = z
  .object({
    botId: UuidSchema,
    intentId: UuidSchema,
    name: z.string().min(1).max(100),
    metadata: z.record(z.any()).optional(),
  })
  .strict();

export const UpdateEntitySchema = CreateEntitySchema.omit({ botId: true, intentId: true })
  .partial()
  .strict();

// Type extraction
export type CreateEntityRequest = z.infer<typeof CreateEntitySchema>;
export type UpdateEntityRequest = z.infer<typeof UpdateEntitySchema>;
