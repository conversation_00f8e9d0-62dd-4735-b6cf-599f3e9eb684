import { z } from "zod";
import { UuidSchema, EntityType, isValidEntityType } from "@neuratalk/common";

// Entity Type validation schema
export const EntityTypeSchema = z.string().refine(isValidEntityType, {
  message: "Invalid entity type. Must be a valid BT.* type",
});

// Entity schemas
export const CreateEntitySchema = z
  .object({
    botId: UuidSchema,
    intentId: UuidSchema.optional(), // Optional for global entities
    displayName: z
      .string()
      .min(1)
      .max(100)
      .regex(/^[a-zA-Z][a-zA-Z0-9_]*$/, {
        message:
          "Display name must start with a letter and contain only letters, numbers, and underscores",
      }),
    entityType: EntityTypeSchema,
    isGlobal: z.boolean().default(false),
    metadata: z.record(z.any()).optional(),
  })
  .strict();

export const UpdateEntitySchema = CreateEntitySchema.omit({ botId: true, intentId: true })
  .partial()
  .strict();

// Type extraction
export type CreateEntityRequest = z.infer<typeof CreateEntitySchema>;
export type UpdateEntityRequest = z.infer<typeof UpdateEntitySchema>;
