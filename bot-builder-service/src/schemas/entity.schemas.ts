import { z } from "zod";
import { UuidSchema, isValidEntityType } from "@neuratalk/common";

export const EntityTypeSchema = z.string().refine(isValidEntityType, {
  message: "Invalid entity type. Must be a valid BT.* type",
});

// Entity schemas
export const CreateEntitySchema = z
  .object({
    botId: UuidSchema,
    intentId: UuidSchema.optional(),
    name: z
      .string()
      .min(1)
      .max(100)
      .regex(/^[a-zA-Z][a-zA-Z0-9\s_-]*$/, {
        message:
          "Name must start with a letter and contain only letters, numbers, spaces, underscores, and hyphens",
      }),
    entityType: EntityTypeSchema,
    metadata: z.record(z.any()).optional(),
  })
  .strict();

export const UpdateEntitySchema = CreateEntitySchema.omit({ botId: true, intentId: true })
  .partial()
  .strict();

// Type extraction
export type CreateEntityRequest = z.infer<typeof CreateEntitySchema>;
export type UpdateEntityRequest = z.infer<typeof UpdateEntitySchema>;
