import { z } from "zod";
import { UuidSchema, PaginationQuerySchema } from "@neuratalk/common";

// Bot Schemas
export const CreateBotRequestSchema = z
  .object({
    name: z.string().min(1).max(255),
    description: z.string().max(1000).optional(),
    settings: z.record(z.any()).optional(),
    metadata: z.record(z.any()).optional(),
    domain: z.string().optional(),
  })
  .strict();

export const UpdateBotRequestSchema = CreateBotRequestSchema.partial().strict();

export const BotIdParamSchema = z
  .object({
    botId: UuidSchema,
  })
  .strict();

// Channel Integration Schemas
export const BotChannelParamSchema = z
  .object({
    botId: UuidSchema,
    channelType: z.string().min(1), // e.g., "web", "whatsapp"
  })
  .strict();

export const CreateChannelIntegrationSchema = z
  .object({
    channelType: z.string().min(1),
    config: z.record(z.any()),
  })
  .strict();

export const BotChannelIdParamSchema = z
  .object({
    botId: UuidSchema,
    channelId: UuidSchema,
  })
  .strict();

export const UpdateChannelIntegrationSchema = CreateChannelIntegrationSchema.partial().strict();

// Flow Schemas
//TODO: update this and rm all unnecessary keys
export const CreateFlowRequestSchema = z
  .object({
    name: z.string().min(1).max(255),
    description: z.string().max(1000).optional(),
    botId: UuidSchema,
    type: z.string().optional(),
    metadata: z.record(z.any()).optional(),
    appData: z.record(z.any()).optional(),
    ngage_id: z.string().optional(),
    owner: z.number().optional(),
    createdBy: z.number().optional(),
    modifiedBy: z.number().optional(),
    svg: z.string().optional(),
    entryNodeId: z.any(),
    nodes: z.any(),
    connections: z.any(),
    isActive: z.boolean().optional(),
  })
  .strict();

export const FlowIdParamSchema = z
  .object({
    id: UuidSchema,
  })
  .strict();

export const UpdateFlowRequestSchema = CreateFlowRequestSchema.omit({ botId: true })
  .partial()
  .strict();

export const FlowAppIdParamSchema = z
  .object({
    id: UuidSchema,
    appId: UuidSchema,
  })
  .strict();

export const GetFlowsQuerySchema = PaginationQuerySchema.extend({
  botId: UuidSchema.optional(),
  isActive: z.boolean().optional(),
}).strict();

export const BulkCreateFlowsRequestSchema = z
  .object({
    flows: z.array(CreateFlowRequestSchema),
  })
  .strict();

// Type exports
export type CreateBotRequest = z.infer<typeof CreateBotRequestSchema>;
export type UpdateBotRequest = z.infer<typeof UpdateBotRequestSchema>;
export type BotIdParam = z.infer<typeof BotIdParamSchema>;
export type BotChannelParam = z.infer<typeof BotChannelParamSchema>;
export type CreateChannelIntegrationRequest = z.infer<typeof CreateChannelIntegrationSchema>;
export type BotChannelIdParam = z.infer<typeof BotChannelIdParamSchema>;
export type UpdateChannelIntegrationRequest = z.infer<typeof UpdateChannelIntegrationSchema>;
export type CreateFlowRequest = z.infer<typeof CreateFlowRequestSchema>;
export type FlowIdParam = z.infer<typeof FlowIdParamSchema>;
export type UpdateFlowRequest = z.infer<typeof UpdateFlowRequestSchema>;
export type FlowAppIdParam = z.infer<typeof FlowAppIdParamSchema>;
export type GetFlowsQuery = z.infer<typeof GetFlowsQuerySchema>;
export type BulkCreateFlowsRequest = z.infer<typeof BulkCreateFlowsRequestSchema>;
