import { z } from "zod";
import { UuidSchema } from "@neuratalk/common";

export const CreateLanguageSchema = z
  .object({
    name: z.string().min(1).max(255),
    code: z.string().min(1).max(10),
  })
  .strict();

export const UpdateLanguageSchema = CreateLanguageSchema.partial().strict();

export const CreateBotLanguageSchema = z
  .object({
    botId: UuidSchema,
    langId: UuidSchema,
    isDefault: z.boolean().optional(),
  })
  .strict();

export const UpdateBotLanguageSchema = z.object({}).strict();

export type CreateLanguageRequest = z.infer<typeof CreateLanguageSchema>;
export type UpdateLanguageRequest = z.infer<typeof UpdateLanguageSchema>;
export type CreateBotLanguageRequest = z.infer<typeof CreateBotLanguageSchema>;
export type UpdateBotLanguageRequest = z.infer<typeof UpdateBotLanguageSchema>;
