import { z } from "zod";
import { UuidSchema } from "@neuratalk/common";

// FAQ Category schemas
export const CreateFaqCategorySchema = z
  .object({
    botId: UuidSchema,
    name: z.string().min(1).max(32),
  })
  .strict();

export const UpdateFaqCategorySchema = CreateFaqCategorySchema.omit({ botId: true })
  .partial()
  .strict();

// FAQ Translation schemas
export const FaqTranslationPayloadSchema = z
  .object({
    id: UuidSchema.optional(),
    langId: UuidSchema,
    questions: z.array(z.string().min(1)).min(1),
    answer: z.string().min(1),
    metadata: z.record(z.any()).optional(),
  })
  .strict();

export const CreateFaqTranslationSchema = z
  .object({
    faqId: UuidSchema.optional(),
    categoryId: UuidSchema,
    botId: UuidSchema,
    flowId: UuidSchema.optional(),
    langId: UuidSchema,
    questions: z.array(z.string().min(1)).min(1),
    answer: z.string().min(1),
    metadata: z.record(z.any()).optional(),
  })
  .strict();

export const UpdateFaqTranslationSchema = CreateFaqTranslationSchema.omit({
  faqId: true,
  langId: true,
  categoryId: true,
  botId: true,
})
  .partial()
  .strict();

export const FaqIdParamSchema = z
  .object({
    faqId: UuidSchema,
  })
  .strict();

export const FaqTranslationByLangParamSchema = z
  .object({
    faqId: UuidSchema,
    langId: UuidSchema,
  })
  .strict();

export const FaqsByCategoryAndLanguageParamSchema = z
  .object({
    categoryId: UuidSchema,
    langId: UuidSchema,
  })
  .strict();

// FAQ Items schemas
export const CreateFaqItemSchema = z
  .object({
    botId: UuidSchema,
    flowId: UuidSchema.optional(),
    categoryId: UuidSchema,
  })
  .strict();

export const UpdateFaqItemSchema = CreateFaqItemSchema.partial().strict();

// Type extraction
export type CreateFaqCategoryRequest = z.infer<typeof CreateFaqCategorySchema>;
export type UpdateFaqCategoryRequest = z.infer<typeof UpdateFaqCategorySchema>;
export type CreateFaqItemRequest = z.infer<typeof CreateFaqItemSchema>;
export type UpdateFaqItemRequest = z.infer<typeof UpdateFaqItemSchema>;
export type CreateFaqTranslationRequest = z.infer<typeof CreateFaqTranslationSchema>;
export type UpdateFaqTranslationRequest = z.infer<typeof UpdateFaqTranslationSchema>;
export type FaqIdParam = z.infer<typeof FaqIdParamSchema>;
export type FaqTranslationByLangParam = z.infer<typeof FaqTranslationByLangParamSchema>;

export type FaqsByCategoryAndLanguageParam = z.infer<typeof FaqsByCategoryAndLanguageParamSchema>;
