import { DatabaseConnection } from "@neuratalk/bot-store";
import { RasaService } from "../services/rasa.service";
import { BotService } from "../services/bot.service";
import { FlowService } from "../services/flow.service";
import { BuildService } from "../services/build.service";

export interface AppContext {
  db: DatabaseConnection;
  rasaService: RasaService;
  botService: BotService;
  flowService: FlowService;
  buildService: BuildService;
}
