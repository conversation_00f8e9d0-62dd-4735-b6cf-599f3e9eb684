import { Router } from 'express';
import { BuildController } from '../controllers/build.controller';
import { AppContext } from '../types/context.types';

export function createBuildRoutes(context: AppContext): Router {
  const router = Router();
  const buildController = new BuildController(context.buildService);
  
  router.post('/:botId/build', buildController.createBuild.bind(buildController));
  
  const botBuildRouter = Router();
  botBuildRouter.use('/bots', router);
  return botBuildRouter;
}