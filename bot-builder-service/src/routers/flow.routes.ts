import { Router } from "express";
import { AppContext } from "../types/context.types";
import { FlowController } from "../controllers/flow.controller";
import { validateBody, validateParams, validateQuery } from "@neuratalk/common";
import {
  CreateFlowRequestSchema,
  FlowIdParamSchema,
  UpdateFlowRequestSchema,
  FlowAppIdParamSchema,
  GetFlowsQuerySchema,
  BotIdParamSchema,
} from "../schemas";

export function createFlowRoutes(context: AppContext): Router {
  const router = Router();
  const flowController = new FlowController(context.flowService);

  router.post("/flows", validateBody(CreateFlowRequestSchema), flowController.createFlow);
  router.get("/flows/:id", validateParams(FlowIdParamSchema), flowController.getFlowById);
  router.put(
    "/flows/:id",
    validateParams(FlowIdParamSchema),
    validateBody(UpdateFlowRequestSchema),
    flowController.updateFlow,
  );
  router.delete(
    "/flows/:id/apps/:appId",
    validateParams(FlowAppIdParamSchema),
    flowController.deleteFlow,
  );
  router.get(
    "/bots/:botId/flows",
    validateParams(BotIdParamSchema),
    validateQuery(GetFlowsQuerySchema),
    flowController.getFlowsByBot,
  );

  return router;
}
