import { Router } from "express";
import { EntitiesController } from "../controllers/entities.controller";
import { authMiddleware } from "../middleware/auth.middleware";
import {
  PaginationQuerySchema,
  UuidParamSchema,
  validateBody,
  validateParams,
  validateQuery,
} from "@neuratalk/common";
import { CreateEntitySchema, UpdateEntitySchema } from "../schemas/entity.schemas";

import { AppContext } from "../types/context.types";

export function createEntityRoutes(context: AppContext): Router {
  const router = Router();

  const entitiesController = new EntitiesController(context);

  router.post(
    "/entities",
    authMiddleware,
    validateBody(CreateEntitySchema),
    entitiesController.create,
  );
  router.get("/entities", validateQuery(PaginationQuerySchema), entitiesController.getAll);
  router.get("/entities/:id", validateParams(UuidParamSchema), entitiesController.getById);
  router.put(
    "/entities/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateEntitySchema),
    entitiesController.update,
  );
  router.delete("/entities/:id", validateParams(UuidParamSchema), entitiesController.delete);

  return router;
}
