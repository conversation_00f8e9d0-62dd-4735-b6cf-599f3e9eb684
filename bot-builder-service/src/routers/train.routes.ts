import { Router } from "express";
import { AppContext } from "../types/context.types";
import { createFaqRoutes } from "./faq.routes";
import { createIntentRoutes } from "./intent.routes";
import { createEntityRoutes } from "./entity.routes";

export function createTrainRoutes(context: AppContext): Router {
  const router = Router();

  const faqRouter = createFaqRoutes(context);
  const intentRouter = createIntentRoutes(context);
  const entityRouter = createEntityRoutes(context);

  router.use(faqRouter);
  router.use(intentRouter);
  router.use(entityRouter);

  return router;
}
