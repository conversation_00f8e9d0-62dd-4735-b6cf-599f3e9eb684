import { Router } from "express";
import { IntentItemsController } from "../controllers/intent-items.controller";
import { UtteranceTranslationController } from "../controllers/utterance-translation.controller";
import { authMiddleware } from "../middleware/auth.middleware";
import {
  PaginationQuerySchema,
  UuidParamSchema,
  validateBody,
  validateParams,
  validateQuery,
} from "@neuratalk/common";
import {
  CreateIntentItemSchema,
  UpdateIntentItemSchema,
  CreateUtteranceTranslationSchema,
  UpdateUtteranceTranslationSchema,
  AssignFlowToIntentSchema,
  UtteranceTranslationParamSchema,
  UtteranceTranslationByLangParamSchema,
} from "../schemas/intent.schemas";

import { AppContext } from "../types/context.types";

export function createIntentRoutes(context: AppContext): Router {
  const router = Router();

  const intentItemsController = new IntentItemsController(context);
  const intentUtteranceTranslationController = new UtteranceTranslationController(context);

  // Intent Items routes
  router.post(
    "/intent-items",
    authMiddleware,
    validateBody(CreateIntentItemSchema),
    intentItemsController.create,
  );
  router.get("/intent-items", validateQuery(PaginationQuerySchema), intentItemsController.getAll);
  router.get("/intent-items/:id", validateParams(UuidParamSchema), intentItemsController.getById);
  router.put(
    "/intent-items/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateIntentItemSchema),
    intentItemsController.update,
  );
  router.delete("/intent-items/:id", validateParams(UuidParamSchema), intentItemsController.delete);
  router.post(
    "/intent-items/assign-flow",
    authMiddleware,
    validateBody(AssignFlowToIntentSchema),
    intentItemsController.assignFlowToIntent,
  );

  // Intent Utterance Translation routes
  router.post(
    "/intent/:intentId/lang/:langId/intent-utterance",
    authMiddleware,
    validateBody(CreateUtteranceTranslationSchema),
    validateParams(UtteranceTranslationParamSchema),
    intentUtteranceTranslationController.create,
  );
  router.get(
    "/intent/:intentId/lang/:langId/intent-utterance",
    validateQuery(PaginationQuerySchema),
    validateParams(UtteranceTranslationParamSchema),
    intentUtteranceTranslationController.getAll,
  );

  router.get(
    "/intent-utterance/:id",
    validateParams(UuidParamSchema),
    intentUtteranceTranslationController.getById,
  );
  router.get(
    "/utterance/:utteranceId/lang/:langId/translation",
    validateParams(UtteranceTranslationByLangParamSchema),
    intentUtteranceTranslationController.getTranslationByUtteranceIdAndLangId,
  );
  router.put(
    "/intent-utterance/:id",
    validateParams(UuidParamSchema),
    validateBody(UpdateUtteranceTranslationSchema),
    intentUtteranceTranslationController.update,
  );
  router.delete(
    "/intent-utterance/:id",
    validateParams(UuidParamSchema),
    intentUtteranceTranslationController.delete,
  );

  return router;
}
