{"name": "@neuratalk/bot-store", "version": "1.0.0", "description": "Shared database layer for bot services", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "npm run clean && tsc", "clean": "rm -rf dist", "migrate": "sequelize-cli db:migrate", "migrate:undo": "sequelize-cli db:migrate:undo", "migrate:create": "sequelize-cli migration:generate --name", "seed:all": "sequelize-cli db:seed:all", "seed:undo:all": "sequelize-cli db:seed:undo:all"}, "keywords": ["chatbot", "database", "sequelize"], "author": "Chatbot Platform Team", "license": "MIT", "dependencies": {"@neuratalk/common": "file:../common", "dotenv": "^16.1.4", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "sqlite3": "^5.1.7"}, "devDependencies": {"@types/node": "^20.3.1", "sequelize-cli": "^6.6.1", "typescript": "^5.2.0"}, "engines": {"node": ">=18.0.0"}}