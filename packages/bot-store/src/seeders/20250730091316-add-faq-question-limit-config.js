'use strict';

const { v4: uuidv4 } = require('uuid');
const { PlatformConfigKey } = require('../models/platformConfig.model');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.bulkInsert('platform_configs', [{
      id: uuidv4(),
      key: PlatformConfigKey.FAQ_QUESTION_LIMIT,
      value: 10,
      createdAt: new Date(),
      updatedAt: new Date(),
    }], {});
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.bulkDelete('platform_configs', { key: PlatformConfigKey.FAQ_QUESTION_LIMIT }, {});
  }
};
