import { QueryInterface } from "sequelize";
import { v4 as uuidv4 } from "uuid";

module.exports = {
  up: async (queryInterface: QueryInterface) => {
    await queryInterface.bulkInsert("languages", [
      {
        id: uuidv4(),
        name: "English",
        code: "en",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "German",
        code: "de",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "French",
        code: "fr",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Spanish",
        code: "es",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Italian",
        code: "it",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Portuguese",
        code: "pt",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Russian",
        code: "ru",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Chinese (Simplified)",
        code: "zh-C<PERSON>",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Japanese",
        code: "ja",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Hindi",
        code: "hi",
        createdAt: new Date(),
      },
    ]);
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.bulkDelete("languages", {}, {});
  },
};
