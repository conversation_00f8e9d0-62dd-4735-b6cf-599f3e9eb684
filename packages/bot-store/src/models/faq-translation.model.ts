import { DataTypes, Model, Optional, Sequelize, BelongsTo, BelongsToGetAssociationMixin } from "sequelize";
import { FaqItemsModel } from "./faq-items.model";
import { LanguageModel } from "./language.model";

export interface FaqTranslationAttributes {
  id: string;
  faqId: string;
  langId: string;
  questions: string[];
  answer: string;
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;

  // Associations
  faqItem?: FaqItemsModel;
  language?: LanguageModel;
}

type FaqTranslationCreationAttributes = Optional<
  FaqTranslationAttributes,
  "id" | "createdAt" | "updatedAt" | "deletedAt" | "deletedBy"
>;

export class FaqTranslationModel
  extends Model<FaqTranslationAttributes, FaqTranslationCreationAttributes>
  implements FaqTranslationAttributes
{
  public id!: string;
  public faqId!: string;
  public langId!: string;
  public questions!: string[];
  public answer!: string;
  public metadata?: Record<string, any>;
  public createdAt!: Date;
  public updatedAt!: Date;
  public deletedAt?: Date;
  public createdBy!: string;
  public updatedBy!: string;
  public deletedBy?: string;

  // Mixins for associations
  public getFaqItem!: BelongsToGetAssociationMixin<FaqItemsModel>;
  public getLanguage!: BelongsToGetAssociationMixin<LanguageModel>;

  // Properties for eager loading
  public faqItem?: FaqItemsModel;
  public language?: LanguageModel;

  public static associations: {
    faqItem: BelongsTo;
    language: BelongsTo;
  };
}

export function initFaqTranslationModel(sequelize: Sequelize): typeof FaqTranslationModel {
  FaqTranslationModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      faqId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "faq_items",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      langId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "languages",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      questions: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      answer: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "FaqTranslationModel",
      tableName: "faq_translations",
      paranoid: true,
      timestamps: true,
    },
  );

  return FaqTranslationModel;
}
