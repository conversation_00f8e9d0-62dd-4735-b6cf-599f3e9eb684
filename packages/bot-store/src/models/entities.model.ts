import {
  DataTypes,
  Model,
  Optional,
  Sequelize,
  BelongsTo,
  BelongsToGetAssociationMixin,
} from "sequelize";
import { BotModel } from "./bot.model";
import { IntentItemsModel } from "./intent-items.model";

export interface EntitiesAttributes {
  id: string;
  name: string; // This will be the final generated name: displayName_id_type
  displayName: string; // User-friendly name (e.g., "product")
  entityType: string; // BT.STRING, BT.NUMBER, etc.
  botId: string;
  intentId?: string; // Made optional to support bot-level entities
  isGlobal: boolean; // Whether entity can be reused across intents
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;

  // Associations
  bot?: BotModel;
  intentItem?: IntentItemsModel;
}

type EntitiesCreationAttributes = Optional<
  EntitiesAttributes,
  | "id"
  | "createdAt"
  | "updatedAt"
  | "deletedAt"
  | "deletedBy"
  | "metadata"
  | "createdBy"
  | "updatedBy"
  | "name"
>;

export class EntitiesModel
  extends Model<EntitiesAttributes, EntitiesCreationAttributes>
  implements EntitiesAttributes
{
  public id!: string;
  public name!: string;
  public displayName!: string;
  public entityType!: string;
  public botId!: string;
  public intentId?: string;
  public isGlobal!: boolean;
  public metadata?: Record<string, any>;
  public createdAt!: Date;
  public updatedAt!: Date;
  public deletedAt?: Date;
  public createdBy!: string;
  public updatedBy!: string;
  public deletedBy?: string;

  // Mixins for associations
  public getBot!: BelongsToGetAssociationMixin<BotModel>;
  public getIntentItem!: BelongsToGetAssociationMixin<IntentItemsModel>;

  // Properties for eager loading
  public bot?: BotModel;
  public intentItem?: IntentItemsModel;

  public static associations: {
    bot: BelongsTo;
    intentItem: BelongsTo;
  };
}

export function initEntitiesModel(sequelize: Sequelize): typeof EntitiesModel {
  EntitiesModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(200), // Increased to accommodate generated names
        allowNull: false,
        unique: true, // Ensure entity names are unique across the system
      },
      displayName: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      entityType: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      botId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "bots",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      intentId: {
        type: DataTypes.UUID,
        allowNull: true, // Made nullable for global entities
        references: {
          model: "intent_items",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      isGlobal: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "EntitiesModel",
      tableName: "entities",
      paranoid: true,
      timestamps: true,
    },
  );

  return EntitiesModel;
}
