/**
 * Training Job Model
 */

import { DataTypes, Model, Sequelize } from "sequelize";

export enum TrainingJobStatus {
  PENDING = "PENDING",
  RUNNING = "RUNNING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
  CANCELLED = "CANCELLED"
}

export interface TrainingJobAttributes {
  id: string;
  botId: string;
  status: TrainingJobStatus;
  filesUrl?: string;
  modelUrl?: string;
  webhookUrl?: string;
  startedAt?: Date;
  completedAt?: Date;
  errorMessage?: string;
  // progress: number;
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
}

export class TrainingJobModel extends Model<TrainingJobAttributes> implements TrainingJobAttributes {
  public id!: string;
  public botId!: string;
  public status!: TrainingJobStatus;
  public filesUrl?: string;
  public modelUrl?: string;
  public webhookUrl?: string;
  public startedAt?: Date;
  public completedAt?: Date;
  public errorMessage?: string;
  public metadata?: Record<string, any>;
  public readonly createdAt?: Date;
  public readonly updatedAt?: Date;
}

export function initTrainingJobModel(sequelize: Sequelize): typeof TrainingJobModel {
  TrainingJobModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      botId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(...Object.values(TrainingJobStatus)),
        defaultValue: TrainingJobStatus.PENDING,
      },
      filesUrl: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      modelUrl: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      webhookUrl: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      startedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      completedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      errorMessage: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      metadata: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      sequelize,
      tableName: "training_jobs",
      indexes: [
        { fields: ["botId"] },
        { fields: ["status"] },
        { fields: ["createdAt"] },
      ],
    }
  );

  return TrainingJobModel;
}