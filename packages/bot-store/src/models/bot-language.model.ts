import {
  DataTypes,
  Model,
  Optional,
  Sequelize,
  BelongsTo,
  BelongsToGetAssociationMixin,
} from "sequelize";
import { BotModel } from "./bot.model";
import { LanguageModel } from "./language.model";

export interface BotLanguageAttributes {
  id: string;
  botId: string;
  langId: string;
  isDefault: boolean;
  createdAt?: Date;
  createdBy: string;

  // Associations
  bot?: BotModel;
  language?: LanguageModel;
}

type BotLanguageCreationAttributes = Optional<BotLanguageAttributes, "id" | "createdAt">;

export class BotLanguageModel
  extends Model<BotLanguageAttributes, BotLanguageCreationAttributes>
  implements BotLanguageAttributes
{
  public id!: string;
  public botId!: string;
  public langId!: string;
  public createdAt!: Date;
  public createdBy!: string;
  public isDefault!: boolean;

  // Mixins for associations
  public getBot!: BelongsToGetAssociationMixin<BotModel>;
  public getLanguage!: BelongsToGetAssociationMixin<LanguageModel>;

  // Properties for eager loading
  public bot?: BotModel;
  public language?: LanguageModel;

  public static associations: {
    bot: BelongsTo;
    language: BelongsTo;
  };
}

export function initBotLanguageModel(sequelize: Sequelize): typeof BotLanguageModel {
  BotLanguageModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      botId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "bots",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      langId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "languages",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      createdAt: {
        type: DataTypes.DATE,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      isDefault: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      sequelize,
      modelName: "BotLanguageModel",
      tableName: "bot_languages",
      timestamps: true,
      updatedAt: false,
    },
  );

  return BotLanguageModel;
}
