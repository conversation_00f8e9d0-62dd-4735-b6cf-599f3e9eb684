import { DataTypes, Model, Optional, Sequelize, BelongsTo, BelongsToGetAssociationMixin } from "sequelize";
import { IntentUtteranceModel } from "./intent-utterance.model";
import { LanguageModel } from "./language.model";

export interface UtteranceTranslationAttributes {
  id: string;
  utteranceId: string;
  langId: string;
  text: string;
  entities?: Record<string, any>; // Or a more specific type for entities
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;

  // Associations
  utterance?: IntentUtteranceModel;
  language?: LanguageModel;
}

type UtteranceTranslationCreationAttributes = Optional<
  UtteranceTranslationAttributes,
  "id" | "createdAt" | "updatedAt" | "deletedAt" | "deletedBy"
>;

export class UtteranceTranslationModel
  extends Model<UtteranceTranslationAttributes, UtteranceTranslationCreationAttributes>
  implements UtteranceTranslationAttributes
{
  public id!: string;
  public utteranceId!: string;
  public langId!: string;
  public text!: string;
  public entities?: Record<string, any>;
  public createdAt!: Date;
  public updatedAt!: Date;
  public deletedAt?: Date;
  public createdBy!: string;
  public updatedBy!: string;
  public deletedBy?: string;

  // Mixins for associations
  public getUtterance!: BelongsToGetAssociationMixin<IntentUtteranceModel>;
  public getLanguage!: BelongsToGetAssociationMixin<LanguageModel>;

  // Properties for eager loading
  public utterance?: IntentUtteranceModel;
  public language?: LanguageModel;

  public static associations: {
    utterance: BelongsTo;
    language: BelongsTo;
  };
}

export function initUtteranceTranslationModel(
  sequelize: Sequelize,
): typeof UtteranceTranslationModel {
  UtteranceTranslationModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      utteranceId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "intent_utterances",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      langId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "languages",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      text: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      entities: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: false
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "UtteranceTranslationModel",
      tableName: "intent_utterance_translations",
      timestamps: true,
      paranoid: true,
    },
  );

  return UtteranceTranslationModel;
}
