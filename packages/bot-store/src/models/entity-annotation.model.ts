import {
  DataTypes,
  Model,
  Optional,
  Sequelize,
  BelongsTo,
  BelongsToGetAssociationMixin,
} from "sequelize";
import { UtteranceTranslationModel } from "./utterance-translation.model";
import { EntitiesModel } from "./entities.model";

export interface EntityAnnotationAttributes {
  id: string;
  utteranceTranslationId: string;
  entityId: string;
  startPosition: number;
  endPosition: number;
  entityValue: string; // The actual text that was annotated
  confidence?: number; // Optional confidence score
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;

  // Associations
  utteranceTranslation?: UtteranceTranslationModel;
  entity?: EntitiesModel;
}

type EntityAnnotationCreationAttributes = Optional<
  EntityAnnotationAttributes,
  | "id"
  | "createdAt"
  | "updatedAt"
  | "deletedAt"
  | "deletedBy"
  | "metadata"
  | "confidence"
  | "createdBy"
  | "updatedBy"
>;

export class EntityAnnotationModel
  extends Model<EntityAnnotationAttributes, EntityAnnotationCreationAttributes>
  implements EntityAnnotationAttributes
{
  public id!: string;
  public utteranceTranslationId!: string;
  public entityId!: string;
  public startPosition!: number;
  public endPosition!: number;
  public entityValue!: string;
  public confidence?: number;
  public metadata?: Record<string, any>;
  public createdAt!: Date;
  public updatedAt!: Date;
  public deletedAt?: Date;
  public createdBy!: string;
  public updatedBy!: string;
  public deletedBy?: string;

  // Mixins for associations
  public getUtteranceTranslation!: BelongsToGetAssociationMixin<UtteranceTranslationModel>;
  public getEntity!: BelongsToGetAssociationMixin<EntitiesModel>;

  // Properties for eager loading
  public utteranceTranslation?: UtteranceTranslationModel;
  public entity?: EntitiesModel;

  public static readonly associations: {
    utteranceTranslation: BelongsTo;
    entity: BelongsTo;
  };
}

export function initEntityAnnotationModel(sequelize: Sequelize): typeof EntityAnnotationModel {
  EntityAnnotationModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      utteranceTranslationId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "utterance_translations",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "entities",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      startPosition: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: {
          min: 0,
        },
      },
      endPosition: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: {
          min: 0,
        },
      },
      entityValue: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      confidence: {
        type: DataTypes.FLOAT,
        allowNull: true,
        validate: {
          min: 0,
          max: 1,
        },
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "EntityAnnotationModel",
      tableName: "entity_annotations",
      paranoid: true,
      timestamps: true,
      indexes: [
        {
          fields: ["utteranceTranslationId"],
        },
        {
          fields: ["entityId"],
        },
        {
          fields: ["startPosition", "endPosition"],
        },
      ],
      validate: {
        positionOrder(this: EntityAnnotationModel) {
          if (this.startPosition >= this.endPosition) {
            throw new Error("Start position must be less than end position");
          }
        },
      },
    },
  );

  return EntityAnnotationModel;
}
