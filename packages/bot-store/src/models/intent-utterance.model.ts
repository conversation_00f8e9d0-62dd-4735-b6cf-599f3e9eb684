import {
  DataTypes,
  Model,
  Optional,
  Sequelize,
  BelongsTo,
  BelongsToGetAssociationMixin,
} from "sequelize";
import { IntentItemsModel } from "./intent-items.model";
import { UtteranceTranslationModel } from "./utterance-translation.model";

export interface IntentUtteranceAttributes {
  id: string;
  intentId: string;
  metadata?: Record<string, any>;
  createdAt?: Date;
  deletedAt?: Date;
  createdBy: string;
  deletedBy?: string;

  // Associations
  intentItem?: IntentItemsModel;
  utteranceTranslations?: UtteranceTranslationModel[];
}

type IntentUtteranceCreationAttributes = Optional<
  IntentUtteranceAttributes,
  "id" | "createdAt" | "deletedAt" | "deletedBy"
>;

export class IntentUtteranceModel
  extends Model<IntentUtteranceAttributes, IntentUtteranceCreationAttributes>
  implements IntentUtteranceAttributes
{
  public id!: string;
  public intentId!: string;
  public metadata?: Record<string, any>;
  public createdAt!: Date;
  public deletedAt?: Date;
  public createdBy!: string;
  public deletedBy?: string;

  // Mixins for associations
  public getIntentItem!: BelongsToGetAssociationMixin<IntentItemsModel>;

  // Properties for eager loading
  public intentItem?: IntentItemsModel;
  public utteranceTranslations?: UtteranceTranslationModel[];

  public static associations: {
    intentItem: BelongsTo;
  };
}

export function initIntentUtteranceModel(sequelize: Sequelize): typeof IntentUtteranceModel {
  IntentUtteranceModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      intentId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "intent_items",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "IntentUtteranceModel",
      tableName: "intent_utterances",
      timestamps: true,
      updatedAt: false,
      paranoid: true,
    },
  );

  return IntentUtteranceModel;
}
