import { DataTypes, Model, Optional, Sequelize } from "sequelize";

export enum PlatformConfigKey {
  FAQ_QUESTION_LIMIT = "faqQuestionLimit",
  // Add your platform-wide configuration keys here
  // Example: API_BASE_URL = "apiBaseUrl",
  // Example: FEATURE_FLAGS = "featureFlags",
  // Example: DEFAULT_LANGUAGE = "defaultLanguage",
}

export interface PlatformConfigAttributes {
  id: string;
  key: PlatformConfigKey; // Use the enum here
  value: any; // Changed to any
  createdAt?: Date;
  updatedAt?: Date;
}

type PlatformConfigCreationAttributes = Optional<PlatformConfigAttributes, "id" | "createdAt" | "updatedAt">;

export class PlatformConfigModel
  extends Model<PlatformConfigAttributes, PlatformConfigCreationAttributes>
  implements PlatformConfigAttributes
{
  public id!: string;
  public key!: PlatformConfigKey; // Use the enum here
  public value!: any; // Changed to any
  public readonly createdAt?: Date;
  public readonly updatedAt?: Date;
}

export function initPlatformConfigModel(sequelize: Sequelize): typeof PlatformConfigModel {
  PlatformConfigModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      key: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      value: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      sequelize,
      modelName: "PlatformConfigModel",
      tableName: "platform_configs",
      timestamps: true,
    },
  );

  return PlatformConfigModel;
}
