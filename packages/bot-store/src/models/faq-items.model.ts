import {
  DataTypes,
  Model,
  Optional,
  Sequelize,
  BelongsTo,
  BelongsToGetAssociationMixin,
} from "sequelize";
import { BotModel } from "./bot.model";
import { FlowModel } from "./flow.model";
import { FaqCategoryModel } from "./faq-category.model";
import { FaqTranslationModel } from "./faq-translation.model";

export interface FaqItemsAttributes {
  id: string;
  botId: string;
  flowId?: string;
  categoryId: string;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;

  // Associations
  bot?: BotModel;
  flow?: FlowModel;
  category?: FaqCategoryModel;
  faqTranslations?: FaqTranslationModel[];
}

type FaqItemsCreationAttributes = Optional<
  FaqItemsAttributes,
  "id" | "createdAt" | "updatedAt" | "deletedAt" | "flowId" | "deletedBy"
>;

export class FaqItemsModel
  extends Model<FaqItemsAttributes, FaqItemsCreationAttributes>
  implements FaqItemsAttributes
{
  public id!: string;
  public botId!: string;
  public flowId?: string;
  public categoryId!: string;
  public createdAt!: Date;
  public updatedAt!: Date;
  public deletedAt?: Date;
  public createdBy!: string;
  public updatedBy!: string;
  public deletedBy?: string;

  // Mixins for associations
  public getBot!: BelongsToGetAssociationMixin<BotModel>;
  public getFlow!: BelongsToGetAssociationMixin<FlowModel>;
  public getCategory!: BelongsToGetAssociationMixin<FaqCategoryModel>;

  // Properties for eager loading
  public bot?: BotModel;
  public flow?: FlowModel;
  public category?: FaqCategoryModel;
  public faqTranslations?: FaqTranslationModel[];

  public static associations: {
    bot: BelongsTo;
    flow: BelongsTo;
    category: BelongsTo;
  };
}

export function initFaqItemsModel(sequelize: Sequelize): typeof FaqItemsModel {
  FaqItemsModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      botId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "bots",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      flowId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "flows",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "SET NULL",
      },
      categoryId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "faq_categories",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "FaqItemsModel",
      tableName: "faq_items",
      paranoid: true,
      timestamps: true,
      indexes: [
        {
          fields: ["botId", "categoryId"],
        },
      ],
    },
  );

  return FaqItemsModel;
}
