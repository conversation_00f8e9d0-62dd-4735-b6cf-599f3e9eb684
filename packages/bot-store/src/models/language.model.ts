import { DataTypes, Model, Optional, Sequelize } from "sequelize";

export interface LanguageAttributes {
  id: string;
  name: string;
  code: string;
  createdAt?: Date;
}

type LanguageCreationAttributes = Optional<LanguageAttributes, "id" | "createdAt">;

export class LanguageModel
  extends Model<LanguageAttributes, LanguageCreationAttributes>
  implements LanguageAttributes
{
  public id!: string;
  public name!: string;
  public code!: string;
  public createdAt?: Date;
}

export function initLanguageModel(sequelize: Sequelize): typeof LanguageModel {
  LanguageModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
      },
      code: {
        type: DataTypes.STRING(10),
        allowNull: false,
        unique: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    {
      sequelize,
      modelName: "LanguageModel",
      tableName: "languages",
      timestamps: true,
      updatedAt: false,
    },
  );

  return LanguageModel;
}
