'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('entity_annotations', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      utteranceTranslationId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'utterance_translations',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      entityId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'entities',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      startPosition: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      endPosition: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      entityValue: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      confidence: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdBy: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      updatedBy: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      deletedBy: {
        type: Sequelize.UUID,
        allowNull: true,
      },
    });

    // Add indexes for better query performance
    await queryInterface.addIndex('entity_annotations', ['utteranceTranslationId']);
    await queryInterface.addIndex('entity_annotations', ['entityId']);
    await queryInterface.addIndex('entity_annotations', ['startPosition', 'endPosition']);
    
    // Add constraint to ensure start position is less than end position
    await queryInterface.addConstraint('entity_annotations', {
      fields: ['startPosition', 'endPosition'],
      type: 'check',
      name: 'check_position_order',
      where: {
        startPosition: {
          [Sequelize.Op.lt]: Sequelize.col('endPosition')
        }
      }
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('entity_annotations');
  }
};
