'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add new columns to entities table
    await queryInterface.addColumn('entities', 'displayName', {
      type: Sequelize.STRING(100),
      allowNull: true, // Temporarily allow null for existing records
    });

    await queryInterface.addColumn('entities', 'entityType', {
      type: Sequelize.STRING(50),
      allowNull: true, // Temporarily allow null for existing records
    });

    await queryInterface.addColumn('entities', 'isGlobal', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });

    // Modify existing columns
    await queryInterface.changeColumn('entities', 'name', {
      type: Sequelize.STRING(200),
      allowNull: false,
      unique: true,
    });

    await queryInterface.changeColumn('entities', 'intentId', {
      type: Sequelize.UUID,
      allowNull: true, // Make nullable for global entities
      references: {
        model: 'intent_items',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    // Update existing records with default values
    await queryInterface.sequelize.query(`
      UPDATE entities 
      SET 
        displayName = COALESCE(name, 'legacy_entity'),
        entityType = 'BT.STRING'
      WHERE displayName IS NULL OR entityType IS NULL
    `);

    // Make the new columns non-nullable after setting default values
    await queryInterface.changeColumn('entities', 'displayName', {
      type: Sequelize.STRING(100),
      allowNull: false,
    });

    await queryInterface.changeColumn('entities', 'entityType', {
      type: Sequelize.STRING(50),
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove the new columns
    await queryInterface.removeColumn('entities', 'displayName');
    await queryInterface.removeColumn('entities', 'entityType');
    await queryInterface.removeColumn('entities', 'isGlobal');

    // Revert name column changes
    await queryInterface.changeColumn('entities', 'name', {
      type: Sequelize.STRING(100),
      allowNull: false,
    });

    // Revert intentId column changes
    await queryInterface.changeColumn('entities', 'intentId', {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'intent_items',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  }
};
