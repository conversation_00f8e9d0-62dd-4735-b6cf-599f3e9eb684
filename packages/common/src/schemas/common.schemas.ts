import { z } from "zod";
import { Order } from "sequelize";

export const FilterConditionSchema = z.object({
  eq: z.any().optional(),
  ne: z.any().optional(),
  gt: z.union([z.number(), z.string().datetime()]).optional(),
  gte: z.union([z.number(), z.string().datetime()]).optional(),
  lt: z.union([z.number(), z.string().datetime()]).optional(),
  lte: z.union([z.number(), z.string().datetime()]).optional(),
  like: z.string().optional(),
  in: z.array(z.any()).optional(),
  notIn: z.array(z.any()).optional(),
  isNull: z.boolean().optional(),
  isNotNull: z.boolean().optional(),
});

export const FilterSchema = z.record(FilterConditionSchema);

export const IncludeQuerySchema = z.object({
  include: z.string().optional(),
});

export const PaginationQuerySchema = z
  .object({
    page: z.coerce.number().int().min(1).default(1),
    limit: z.coerce.number().int().min(1).max(100).default(20),
    search: z.string().optional(),
    order: z
      .string()
      .optional()
      .transform((val) => {
        if (!val) return undefined;
        try {
          return JSON.parse(val) as Order;
        } catch {
          return undefined;
        }
      }),
    filter: z
      .string()
      .optional()
      .transform((val) => {
        if (!val) return undefined;
        try {
          return JSON.parse(val) as FilterObject;
        } catch {
          return undefined;
        }
      })
      .pipe(FilterSchema.optional()),
  })
  .merge(IncludeQuerySchema);

export const UuidParamSchema = z.object({
  id: z.string().uuid(),
});

export const UuidSchema = z.string().uuid();
export const TimestampSchema = z.string().datetime();

//types
export type FilterCondition = z.infer<typeof FilterConditionSchema>;
export type FilterObject = z.infer<typeof FilterSchema>;
export type IncludeQuery = Partial<z.infer<typeof IncludeQuerySchema>>;
export type PaginationQuery = Partial<z.infer<typeof PaginationQuerySchema>>;
export type UuidParams = z.infer<typeof UuidParamSchema>;
