import { Kafka, Consumer, EachMessagePayload, logLevel } from 'kafkajs';
import { logger } from '../logger';

export interface KafkaConsumerConfig {
  brokers: string[];
  groupId: string;
}

export type MessageHandler = (payload: EachMessagePayload) => Promise<void>;

export class KafkaConsumer {
  private kafka: Kafka;
  private consumer: Consumer;
  private isConnected = false;

  constructor(config: KafkaConsumerConfig) {
    this.kafka = new Kafka({
      brokers: config.brokers,
      logLevel: logLevel.WARN,
    });
    this.consumer = this.kafka.consumer({ groupId: config.groupId });
  }

  public async connect(): Promise<void> {
    if (this.isConnected) return;
    try {
      await this.consumer.connect();
      this.isConnected = true;
      logger.info(`Kafka consumer connected for group '${this.consumer.events.GROUP_JOIN}'`);
    } catch (error) {
      logger.error('Failed to connect Kafka consumer:', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    if (!this.isConnected) return;
    try {
      await this.consumer.disconnect();
      this.isConnected = false;
      logger.info('Kafka consumer disconnected.');
    } catch (error) {
      logger.error('Error disconnecting Kafka consumer:', error);
    }
  }

  public async subscribeAndRun(topic: string, handler: MessageHandler): Promise<void> {
    if (!this.isConnected) {
      throw new Error('Kafka consumer is not connected. Cannot subscribe.');
    }
    await this.consumer.subscribe({ topic, fromBeginning: true });
    logger.info(`Subscribed to topic '${topic}'`);

    await this.consumer.run({
      eachMessage: async (payload: EachMessagePayload) => {
        try {
          await handler(payload);
        } catch (error) {
          logger.error(`Error in message handler for topic '${topic}':`, {
            error,
            partition: payload.partition,
            offset: payload.message.offset,
          });
          // TODO: DLQ
        }
      },
    });
  }
}