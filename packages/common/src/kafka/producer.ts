import { <PERSON><PERSON><PERSON>, Producer, ProducerRecord, logLevel } from 'kafkajs';
import { logger } from '../logger';

export interface KafkaProducerConfig {
  clientId: string;
  brokers: string[];
}

export class KafkaProducer {
  private kafka: Kafka;
  private producer: Producer;
  private isConnected = false;

  constructor(config: KafkaProducerConfig) {
    this.kafka = new Kafka({
      ...config,
      logLevel: logLevel.WARN, 
      retry: {
        initialRetryTime: 300,
        retries: 5,
      },
    });
    this.producer = this.kafka.producer({
      idempotent: true, 
      maxInFlightRequests: 5,
    });
  }

  public async connect(): Promise<void> {
    if (this.isConnected) return;
    try {
      await this.producer.connect();
      this.isConnected = true;
      logger.info('Kafka producer connected successfully.');
    } catch (error) {
      logger.error('Failed to connect Kafka producer:', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    if (!this.isConnected) return;
    try {
      await this.producer.disconnect();
      this.isConnected = false;
      logger.info('Kafka producer disconnected successfully.');
    } catch (error) {
      logger.error('Error disconnecting Kafka producer:', error);
    }
  }

  public async sendMessage(record: Omit<ProducerRecord, 'acks'>): Promise<void> {
    if (!this.isConnected) {
      throw new Error('Kafka producer is not connected. Cannot send message.');
    }

    try {
      await this.producer.send(record);
      logger.debug(`Message sent to topic ${record.topic}`);
    } catch (error) {
      logger.error(`Failed to send message to topic ${record.topic}:`, error);
    }
  }
}