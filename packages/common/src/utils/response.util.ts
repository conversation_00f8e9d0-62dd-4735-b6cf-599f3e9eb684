import { ApiResponse } from "../types/api.types";

export const successResponse = <T>(data: T): ApiResponse<T> => {
  return {
    success: true,
    data,
    timestamp: new Date(),
  };
};

export const errorResponse = ({
  error,
  code,
  message = "An unknown error occurred",
  details,
}: {
  error?: unknown; // Made optional
  code: string;
  message?: string;
  details?: any;
}): ApiResponse<any> => {
  return {
    success: false,
    error: {
      code,
      message: error instanceof Error ? error.message : message,
      ...(details && { details }),
    },
    timestamp: new Date(),
  };
};
