/**
 * Entity Types for Chatbot Builder
 *
 * Defines all supported entity types and their Rasa compatibility
 */

export enum EntityType {
  // Basic types
  STRING = "STRING",
  NUMBER = "NUMBER",
  DATE = "DATE",
  TIME = "TIME",
  EMAIL = "EMAIL",
  PHONENUMBER = "PHONENUMBER",

  // Location types
  LOCATION = "LOCATION",
  AIRPORT = "AIRPORT",

  // Person types
  FULLNAME = "FULLNAME",

  // Visual types
  COLOR = "COLOR",
  ATTACHMENT = "ATTACHMENT",
  ATTACHMENT_OR_STRING = "ATTACHMENT_OR_STRING",

  // Date variations
  FUTUREDATE = "FUTUREDATE",
  PASTDATE = "PASTDATE",
  CUSTOMDATE = "CUSTOMDATE",

  // Advanced types
  ENUM_LIST = "ENUM_LIST",
  REGEX = "REGEX",
  CUSTOM = "CUSTOM",
  LANGUAGE = "LANGUAGE",
}

/**
 * Entity types that are compatible with Ra<PERSON>'s built-in extractors
 */
export const RASA_COMPATIBLE_TYPES = [
  EntityType.STRING,
  EntityType.NUMBER,
  EntityType.EMAIL,
  EntityType.REGEX,
  EntityType.CUSTOM,
  EntityType.ENUM_LIST,
];

/**
 * Entity types that require custom implementation or external services
 */
export const EXTERNAL_SERVICE_TYPES = [
  EntityType.DATE,
  EntityType.TIME,
  EntityType.LOCATION,
  EntityType.AIRPORT,
  EntityType.FULLNAME,
  EntityType.COLOR,
  EntityType.PHONENUMBER,
  EntityType.FUTUREDATE,
  EntityType.PASTDATE,
  EntityType.CUSTOMDATE,
  EntityType.LANGUAGE,
];

/**
 * Entity types that are not recommended for Rasa-only implementation
 */
export const UNSUPPORTED_TYPES = [EntityType.ATTACHMENT, EntityType.ATTACHMENT_OR_STRING];

/**
 * Get entity type metadata including Rasa compatibility
 */
export function getEntityTypeInfo(entityType: EntityType) {
  return {
    type: entityType,
    isRasaCompatible: RASA_COMPATIBLE_TYPES.includes(entityType),
    requiresExternalService: EXTERNAL_SERVICE_TYPES.includes(entityType),
    isSupported: !UNSUPPORTED_TYPES.includes(entityType),
    description: getEntityTypeDescription(entityType),
  };
}

/**
 * Get human-readable description for entity types
 */
function getEntityTypeDescription(entityType: EntityType): string {
  const descriptions: Record<EntityType, string> = {
    [EntityType.STRING]: "General text string",
    [EntityType.NUMBER]: "Numeric values (integers or decimals)",
    [EntityType.DATE]: "Date values in various formats",
    [EntityType.TIME]: "Time values",
    [EntityType.EMAIL]: "Email addresses",
    [EntityType.PHONENUMBER]: "Phone numbers",
    [EntityType.LOCATION]: "Geographic locations",
    [EntityType.AIRPORT]: "Airport codes and names",
    [EntityType.FULLNAME]: "Person's full name",
    [EntityType.COLOR]: "Color names or codes",
    [EntityType.ATTACHMENT]: "File attachments",
    [EntityType.ATTACHMENT_OR_STRING]: "File attachments or text",
    [EntityType.FUTUREDATE]: "Future dates only",
    [EntityType.PASTDATE]: "Past dates only",
    [EntityType.CUSTOMDATE]: "Custom date format",
    [EntityType.ENUM_LIST]: "Predefined list of values",
    [EntityType.REGEX]: "Pattern-based matching",
    [EntityType.CUSTOM]: "Custom entity logic",
    [EntityType.LANGUAGE]: "Language codes",
  };

  return descriptions[entityType] || "Unknown entity type";
}

/**
 * Generate the final entity name using the naming convention
 */
export function generateEntityName(
  displayName: string,
  id: string,
  entityType: EntityType,
): string {
  // Clean display name (remove special characters, convert to lowercase)
  const cleanDisplayName = displayName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, "_")
    .replace(/_+/g, "_")
    .replace(/^_|_$/g, "");

  // Extract short ID (first 8 characters)
  const shortId = id.replace(/-/g, "").substring(0, 8);

  return `${cleanDisplayName}_${shortId}_${entityType}`;
}

/**
 * Validate entity type
 */
export function isValidEntityType(type: string): type is EntityType {
  return Object.values(EntityType).includes(type as EntityType);
}

/**
 * Get all supported entity types for UI
 */
export function getSupportedEntityTypes() {
  return Object.values(EntityType)
    .filter((type) => !UNSUPPORTED_TYPES.includes(type))
    .map((type) => {
      const info = getEntityTypeInfo(type);
      return {
        value: type,
        label: type,
        ...info,
      };
    });
}
