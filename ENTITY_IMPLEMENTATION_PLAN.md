# Entity Management System Implementation Plan

## Overview
This document outlines the comprehensive plan for implementing a chatbot builder with entity management system using Rasa for intent classification and entity extraction.

## ✅ Phase 1: Database and Model Enhancement (COMPLETED)

### 1.1 Enhanced Entity Model
- ✅ Extended `EntitiesModel` with new fields:
  - `displayName`: User-friendly name (e.g., "product")
  - `entityType`: BT.STRING, BT.NUMBER, etc. (19 predefined types)
  - `isGlobal`: Whether entity can be reused across intents
  - `name`: Auto-generated using convention `<displayName>_<entityId>_<entityType>`
- ✅ Updated database schema with migration `20241230000001-enhance-entities-table.js`
- ✅ Made `intentId` optional to support bot-level global entities

### 1.2 Entity Type System
- ✅ Created comprehensive entity type definitions in `packages/common/src/types/entity-types.ts`
- ✅ Defined 19 entity types with Rasa compatibility mapping:
  - **Rasa Compatible**: BT.STRING, BT.NUMBER, BT.EMAIL, BT.REGEX, BT.CUSTOM, BT.ENUM_LIST
  - **External Service**: BT.DATE, BT.TIME, BT.LOCATION, BT.AIRPORT, etc.
  - **Unsupported**: BT.ATTACHMENT, BT.ATTACHMENT_OR_STRING
- ✅ Implemented entity name generation function with naming convention
- ✅ Added validation and utility functions

### 1.3 Entity Annotation System
- ✅ Created `EntityAnnotationModel` for storing entity annotations in utterances
- ✅ Database schema with migration `20241230000002-create-entity-annotations-table.js`
- ✅ Supports position-based annotation (startPosition, endPosition)
- ✅ Links utterance translations to entity references
- ✅ Includes validation for non-overlapping annotations

### 1.4 Updated Schemas and Validation
- ✅ Enhanced entity creation schemas with new fields
- ✅ Added entity type validation using custom Zod refinements
- ✅ Created comprehensive annotation schemas with overlap detection
- ✅ Updated entity controller to generate proper entity names

## 🔄 Phase 2: API Enhancement (IN PROGRESS)

### 2.1 Entity Management APIs
- ✅ Updated entity creation to generate proper names
- 🔲 Add entity type management endpoints
- 🔲 Implement global entity listing and filtering
- 🔲 Add entity reuse validation across intents

### 2.2 Entity Annotation APIs
- 🔲 Create entity annotation controller
- 🔲 Implement bulk annotation endpoints
- 🔲 Add text annotation with entity tagging
- 🔲 Create annotation validation and conflict resolution

### 2.3 Enhanced Entity Operations
- 🔲 Entity search and filtering by type
- 🔲 Entity usage tracking across intents
- 🔲 Entity validation for Rasa compatibility
- 🔲 Entity migration tools for existing data

## 📋 Phase 3: Training Service Integration (PENDING)

### 3.1 Rasa File Generation Enhancement
- 🔲 Update `training-service/src/services/rasa_generator.py`
- 🔲 Generate proper entity annotations in nlu.yml:
  ```yaml
  - intent: product_inquiry
    examples: |
      - I want to buy [iPhone](product_abc123_BT.STRING)
      - Show me [Samsung Galaxy](product_def456_BT.STRING)
  ```
- 🔲 Update domain.yml to include entity definitions
- 🔲 Handle entity type mapping for Rasa compatibility

### 3.2 Training Pipeline Updates
- 🔲 Modify training data preparation to include annotations
- 🔲 Add entity validation before training
- 🔲 Implement entity type-specific processing
- 🔲 Add support for external entity services

## 🎯 Phase 4: Frontend Integration (PENDING)

### 4.1 Entity Management UI
- 🔲 Entity type selection dropdown
- 🔲 Global entity management interface
- 🔲 Entity reuse across intents
- 🔲 Entity validation feedback

### 4.2 Annotation Interface
- 🔲 Text highlighting for entity annotation
- 🔲 Drag-and-drop entity tagging
- 🔲 Visual annotation conflict resolution
- 🔲 Real-time annotation preview

## 🧪 Phase 5: Testing and Validation (PENDING)

### 5.1 Database Migrations
- 🔲 Run migrations: `npm run migrate` in bot-store
- 🔲 Test data migration for existing entities
- 🔲 Validate foreign key constraints

### 5.2 API Testing
- 🔲 Test entity creation with new naming convention
- 🔲 Test annotation creation and validation
- 🔲 Test entity reuse across intents
- 🔲 Test Rasa file generation with annotations

### 5.3 Integration Testing
- 🔲 End-to-end entity annotation workflow
- 🔲 Rasa training with annotated entities
- 🔲 Entity extraction validation
- 🔲 Performance testing with large datasets

## 📁 Key Files Modified/Created

### Database Models
- `packages/bot-store/src/models/entities.model.ts` - Enhanced entity model
- `packages/bot-store/src/models/entity-annotation.model.ts` - New annotation model
- `packages/bot-store/migrations/20241230000001-enhance-entities-table.js`
- `packages/bot-store/migrations/20241230000002-create-entity-annotations-table.js`

### Type Definitions
- `packages/common/src/types/entity-types.ts` - Entity type system

### API Schemas
- `bot-builder-service/src/schemas/entity.schemas.ts` - Updated entity schemas
- `bot-builder-service/src/schemas/entity-annotation.schemas.ts` - New annotation schemas

### Controllers
- `bot-builder-service/src/controllers/entities.controller.ts` - Enhanced entity controller

## 🚀 Next Immediate Steps

1. **Run Database Migrations**
   ```bash
   cd packages/bot-store
   npm run migrate
   ```

2. **Create Entity Annotation Controller**
   - Implement CRUD operations for annotations
   - Add bulk annotation endpoints
   - Add validation and conflict resolution

3. **Update Training Service**
   - Modify Python Rasa generator to handle annotations
   - Generate proper nlu.yml with entity markup
   - Update domain.yml with entity definitions

4. **Test Entity Creation**
   - Test new entity creation API
   - Verify entity name generation
   - Test entity type validation

## 🔧 Technical Considerations

### Entity Naming Convention
- Format: `<displayName>_<entityId>_<entityType>`
- Example: `product_abc12345_BT.STRING`
- Ensures uniqueness and Rasa compatibility

### Annotation Storage
- Position-based annotation (start/end positions)
- Non-overlapping validation
- Entity value extraction from text
- Confidence scoring support

### Rasa Compatibility
- Entity types mapped to Rasa extractors
- External service integration for complex types
- Fallback to custom extractors when needed

This implementation provides a solid foundation for the entity management system with proper database design, type safety, and extensibility for future enhancements.
