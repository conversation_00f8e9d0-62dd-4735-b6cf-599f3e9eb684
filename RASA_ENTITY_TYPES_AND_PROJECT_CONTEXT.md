# Rasa Entity Types and Chatbot Builder Project Context

## Project Overview

This document provides comprehensive information about Rasa entity types and extractors, along with detailed context about our chatbot builder project for use with AI models.

### Project Architecture

We are building a **UI-based chatbot builder** with the following key components:

1. **Frontend**: React-based UI for chatbot building
2. **Backend Services**:
   - `bot-builder-service`: Main API for chatbot management
   - `bot-interaction-service`: Handles user interactions
   - `training-service`: Python service for Rasa integration
3. **Database**: PostgreSQL with Sequelize ORM
4. **AI Integration**: Rasa for intent classification and entity extraction (NOT dialogue management)

### Entity Management System

#### Current Implementation Status

- ✅ **Simplified Entity Model**: Removed `displayName` and `isGlobal` columns
- ✅ **Entity Naming**: `name` field with spaces replaced by hyphens
- ✅ **Global vs Local**: Determined by `intentId` presence (null = global, non-null = local)
- ✅ **Entity Types**: 19 predefined BT.\* types (BT.STRING, BT.NUMBER, etc.)
- ✅ **Embedded Format**: Entities embedded directly in utterances like `[text](entity_name)`
- ❌ **Entity Annotation Table**: Removed (no longer needed)

#### Entity Naming Convention

**Original Plan**: `<displayName>_<entityId>_<entityType>` (e.g., "product_abc123_BT.STRING")
**Current Implementation**: Simple name with hyphens (e.g., "product-name")

#### Entity Types (19 Predefined Types)

```typescript
export enum EntityType {
  BT_STRING = "BT.STRING",
  BT_NUMBER = "BT.NUMBER",
  BT_INTEGER = "BT.INTEGER",
  BT_FLOAT = "BT.FLOAT",
  BT_BOOLEAN = "BT.BOOLEAN",
  BT_DATE = "BT.DATE",
  BT_TIME = "BT.TIME",
  BT_DATETIME = "BT.DATETIME",
  BT_EMAIL = "BT.EMAIL",
  BT_PHONE = "BT.PHONE",
  BT_URL = "BT.URL",
  BT_CURRENCY = "BT.CURRENCY",
  BT_PERCENTAGE = "BT.PERCENTAGE",
  BT_LOCATION = "BT.LOCATION",
  BT_PERSON = "BT.PERSON",
  BT_ORGANIZATION = "BT.ORGANIZATION",
  BT_PRODUCT = "BT.PRODUCT",
  BT_EVENT = "BT.EVENT",
  BT_CUSTOM = "BT.CUSTOM",
}
```

## Rasa Entity Types and Extractors - Detailed Analysis

### Key Finding: Entity Types in Rasa Context

**Important**: Rasa does NOT have built-in validation or special handling for custom entity types like our BT.\* types. Entity types in Rasa are simply labels used for:

1. **Organization**: Grouping similar entities
2. **Training**: Helping models learn patterns
3. **Application Logic**: Used by your application for validation and processing
4. **Slot Mapping**: Connecting entities to dialogue slots

### Rasa Entity Extractors

#### 1. DIETClassifier (Dual Intent and Entity Transformer)

- **Primary Use**: Combined intent classification and entity extraction
- **Training**: Learns from annotated training examples
- **Entity Types**: Handles any custom entity types you define
- **Strengths**:
  - State-of-the-art performance
  - Handles context and relationships
  - Can extract multiple entity types simultaneously
- **Limitations**: Requires sufficient training data

#### 2. SpacyEntityExtractor

- **Primary Use**: Pre-trained entity recognition
- **Training**: Uses spaCy's pre-trained models
- **Entity Types**: Fixed set (PERSON, ORG, GPE, MONEY, DATE, etc.)
- **Strengths**:
  - Works out-of-the-box
  - Good for common entity types
  - No training required
- **Limitations**:
  - Cannot extract custom BT.\* types
  - Limited to spaCy's predefined entities

#### 3. RegexEntityExtractor

- **Primary Use**: Pattern-based extraction
- **Training**: Uses regex patterns defined in training data
- **Entity Types**: Any type you define with patterns
- **Strengths**:
  - Perfect for structured data (emails, phone numbers, IDs)
  - Deterministic results
  - Good for BT.EMAIL, BT.PHONE, BT.URL types
- **Limitations**:
  - Cannot handle variations well
  - Requires explicit patterns

#### 4. DucklingEntityExtractor

- **Primary Use**: Structured entity extraction
- **Training**: Uses Facebook's Duckling library
- **Entity Types**: Time, numbers, amounts, distances, etc.
- **Strengths**:
  - Excellent for BT.DATE, BT.TIME, BT.NUMBER, BT.CURRENCY
  - Handles natural language variations
  - Multi-language support
- **Limitations**:
  - Limited to Duckling's supported types
  - Requires Duckling server

### How Our BT.\* Entity Types Work with Rasa

#### Training Data Format

```yaml
nlu:
  - intent: book_flight
    examples: |
      - I want to fly to [New York](BT.LOCATION) on [tomorrow](BT.DATE)
      - Book a flight to [London](BT.LOCATION) for [2 people](BT.NUMBER)
      - I need a ticket to [Paris](BT.LOCATION) costing [$500](BT.CURRENCY)
```

#### Domain Configuration

```yaml
entities:
  - BT.LOCATION
  - BT.DATE
  - BT.NUMBER
  - BT.CURRENCY
  - BT.PERSON
  # ... other BT.* types

slots:
  destination:
    type: text
    mappings:
      - type: from_entity
        entity: BT.LOCATION

  travel_date:
    type: text
    mappings:
      - type: from_entity
        entity: BT.DATE
```

#### Recommended Extractor Configuration

```yaml
pipeline:
  - name: WhitespaceTokenizer
  - name: RegexFeaturizer
  - name: LexicalSyntacticFeaturizer
  - name: CountVectorsFeaturizer
  - name: DIETClassifier
    epochs: 100
  - name: DucklingEntityExtractor
    dimensions: ["time", "number", "amount-of-money"]
  - name: RegexEntityExtractor
  - name: EntitySynonymMapper
```

### Entity Type Validation Strategy

Since Rasa doesn't validate entity types, we implement validation in our application:

1. **Training Time**: Validate BT.\* types when generating Rasa files
2. **Runtime**: Validate extracted entities against expected types
3. **Custom Actions**: Implement type-specific validation logic

### Current Database Schema

```sql
-- Simplified entities table
CREATE TABLE entities (
  id UUID PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE, -- Simple name with hyphens
  entity_type VARCHAR(50) NOT NULL,  -- BT.STRING, BT.NUMBER, etc.
  bot_id UUID NOT NULL REFERENCES bots(id),
  intent_id UUID REFERENCES intent_items(id), -- NULL = global, non-null = local
  metadata JSONB,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP,
  created_by UUID NOT NULL,
  updated_by UUID NOT NULL,
  deleted_by UUID
);
```

### Embedded Entity Format

Entities are embedded directly in utterance text:

```
"I want to buy [shoes](product-name) for [$50](price)"
```

This format will be converted to Rasa training format:

```yaml
- intent: purchase_intent
  examples: |
    - I want to buy [shoes](product-name) for [$50](price)
```

### Next Steps Required

1. **Update Initial Schema**: Modify the original entity schema definitions
2. **Implement Embedded Entity Parsing**: Add logic to parse embedded entities from utterances
3. **Create Rasa File Generation**: Build service to convert our format to Rasa nlu.yml/domain.yml
4. **Add Entity Validation**: Implement BT.\* type validation in custom actions
5. **Complete Missing Endpoints**: Add any missing CRUD endpoints for entities

### File Structure

```
packages/
├── bot-store/
│   ├── src/models/entities.model.ts (✅ Updated)
│   └── migrations/ (✅ Updated)
├── common/
│   └── src/types/entity-types.ts (✅ Created)
└── bot-builder-service/
    ├── src/controllers/entities.controller.ts (✅ Updated)
    └── src/schemas/entity.schemas.ts (✅ Updated)
```

### Detailed Extractor Configurations

#### DIETClassifier Configuration

```yaml
- name: DIETClassifier
  epochs: 300
  hidden_layers_sizes:
    text: [256, 128]
    label: [256, 128]
  number_of_transformer_layers: 2
  transformer_size: 256
  use_masked_language_model: False
  BILOU_flag: True # Important for entity extraction
  entity_recognition: True
  intent_classification: True
```

#### DucklingEntityExtractor Configuration

```yaml
- name: DucklingEntityExtractor
  url: http://localhost:8000
  dimensions:
    - "amount-of-money" # For BT.CURRENCY
    - "distance"
    - "duration"
    - "email" # For BT.EMAIL
    - "number" # For BT.NUMBER, BT.INTEGER, BT.FLOAT
    - "ordinal"
    - "phone-number" # For BT.PHONE
    - "quantity"
    - "temperature"
    - "time" # For BT.DATE, BT.TIME, BT.DATETIME
    - "url" # For BT.URL
    - "volume"
  locale: "en_US"
  timezone: "UTC"
```

#### RegexEntityExtractor Configuration

```yaml
- name: RegexEntityExtractor
  case_sensitive: False
  use_lookup_tables: True
  use_regexes: True
  use_word_boundaries: True
```

#### SpacyEntityExtractor Configuration

```yaml
- name: SpacyEntityExtractor
  dimensions:
    - "PERSON" # Maps to BT.PERSON
    - "ORG" # Maps to BT.ORGANIZATION
    - "GPE" # Maps to BT.LOCATION
    - "PRODUCT" # Maps to BT.PRODUCT
    - "EVENT" # Maps to BT.EVENT
    - "MONEY" # Maps to BT.CURRENCY
    - "DATE" # Maps to BT.DATE
    - "TIME" # Maps to BT.TIME
```

### Entity Type Mapping Strategy

Our BT.\* types map to Rasa extractors as follows:

| BT Type         | Primary Extractor       | Secondary Extractor  | Validation          |
| --------------- | ----------------------- | -------------------- | ------------------- |
| BT.STRING       | DIETClassifier          | -                    | Length, format      |
| BT.NUMBER       | DucklingEntityExtractor | DIETClassifier       | Numeric validation  |
| BT.INTEGER      | DucklingEntityExtractor | DIETClassifier       | Integer validation  |
| BT.FLOAT        | DucklingEntityExtractor | DIETClassifier       | Float validation    |
| BT.BOOLEAN      | DIETClassifier          | RegexEntityExtractor | true/false/yes/no   |
| BT.DATE         | DucklingEntityExtractor | DIETClassifier       | Date format         |
| BT.TIME         | DucklingEntityExtractor | DIETClassifier       | Time format         |
| BT.DATETIME     | DucklingEntityExtractor | DIETClassifier       | DateTime format     |
| BT.EMAIL        | DucklingEntityExtractor | RegexEntityExtractor | Email regex         |
| BT.PHONE        | DucklingEntityExtractor | RegexEntityExtractor | Phone regex         |
| BT.URL          | DucklingEntityExtractor | RegexEntityExtractor | URL regex           |
| BT.CURRENCY     | DucklingEntityExtractor | DIETClassifier       | Currency validation |
| BT.PERCENTAGE   | RegexEntityExtractor    | DIETClassifier       | Percentage regex    |
| BT.LOCATION     | SpacyEntityExtractor    | DIETClassifier       | Location validation |
| BT.PERSON       | SpacyEntityExtractor    | DIETClassifier       | Person validation   |
| BT.ORGANIZATION | SpacyEntityExtractor    | DIETClassifier       | Org validation      |
| BT.PRODUCT      | SpacyEntityExtractor    | DIETClassifier       | Product validation  |
| BT.EVENT        | SpacyEntityExtractor    | DIETClassifier       | Event validation    |
| BT.CUSTOM       | DIETClassifier          | -                    | Custom validation   |

### Training Data Generation

Our system will generate Rasa training data from the embedded format:

**Input (Our Format):**

```
"I want to book [2](BT.NUMBER) tickets to [Paris](BT.LOCATION) on [tomorrow](BT.DATE)"
```

**Output (Rasa Format):**

```yaml
- intent: book_tickets
  examples: |
    - I want to book [2](BT.NUMBER) tickets to [Paris](BT.LOCATION) on [tomorrow](BT.DATE)
```

### Validation Implementation

```typescript
// Entity validation service
export class EntityValidationService {
  validateEntity(value: string, entityType: EntityType): boolean {
    switch (entityType) {
      case EntityType.BT_EMAIL:
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      case EntityType.BT_PHONE:
        return /^\+?[\d\s\-\(\)]+$/.test(value);
      case EntityType.BT_NUMBER:
        return !isNaN(Number(value));
      case EntityType.BT_INTEGER:
        return Number.isInteger(Number(value));
      case EntityType.BT_BOOLEAN:
        return ["true", "false", "yes", "no", "1", "0"].includes(value.toLowerCase());
      // ... other validations
      default:
        return true;
    }
  }
}
```

This document provides the complete context for continuing development of the chatbot builder with proper Rasa integration and entity management.
