# Clean Journey Context Implementation

## Overview
Minimal implementation of journey context with proper separation of concerns - plugin execution logic stays in plugins, orchestration in flow-engine.

## Key Components

### 1. ConvModulePlugin Base Class
**Location**: `/studio/pm_fsync/plugins/convModules/convModulePlugin.js`

```javascript
// Minimal context management methods
updateJourneyContext(context, data = {}) // Update journey data
getGlobalContext(context) // Access global session data  
storeUserInput(context, fieldName, value) // Store user inputs globally
```

### 2. FlowConnectorPlugin
**Location**: `/studio/pm_fsync/plugins/convModules/flowConnector/`

- Added `preserveContext` property (defaults to `true`)
- Plugin only sets the flag, doesn't handle orchestration
- Flow-engine handles the actual context preservation logic

### 3. Flow Engine Orchestration
**Location**: `/bot-interaction-service/src/engine/flow-engine.ts`

```typescript
// Context preservation logic
if (flowAction.preserveContext === false) {
  // Clear journey context but preserve global session data
  const globalContext = currentContext.journeyContext.globalContext;
  currentContext.journeyContext = {
    globalContext,
    channelType: currentContext.journeyContext.channelType,
    language: currentContext.journeyContext.language,
    sessionTimeout: currentContext.journeyContext.sessionTimeout
  };
}
```

### 4. Journey Context Structure
```typescript
interface JourneyContext {
  channelType?: ChannelType;
  language?: string;
  sessionTimeout?: number;
  sessionContext?: Record<string, any>;
  formData?: Record<string, Record<string, string>>;
  awaitingInput?: boolean;
  globalContext?: {
    sessionData: Record<string, any>;
    userInputHistory: Record<string, { value: any; timestamp: string }>;
  };
  flowAction?: {
    journeyId: string;
    flowId: string;
    resumeFromThisContext: boolean;
    passExistingContext: boolean;
    preserveContext?: boolean;
  };
}
```

## New Conversation Nodes

### 1. RequestNode
- HTTP/API requests with channel-specific configs
- Stores responses in global context automatically

### 2. NotificationNode  
- Multi-language notifications
- Channel-specific templates

### 3. InteractiveMessageNode
- Interactive messages with buttons
- Multi-language support

### 4. FeedbackNode
- User feedback collection
- Customizable forms and rating systems

### 5. ScriptNode
- Custom JavaScript execution
- Safe execution environment with context access

### 6. PaymentNode
- Payment transaction handling
- Multiple payment providers support

### 7. LanguageNode
- Language detection and switching
- Auto-detection capabilities

## Context Behavior

### preserveContext: true (Default)
- Journey data flows through all flow transitions
- Context accumulates across nested flows
- Global session data always preserved

### preserveContext: false
- Journey context cleared at that flow connector
- Global session data preserved
- Subsequent flows start with clean journey context

### Journey Completion
- All journey-specific data cleared
- Global session data remains available
- New journeys can access global data

## Plugin Implementation Pattern

```javascript
exec(context) {
  return new Promise(async (resolve) => {
    try {
      // Plugin-specific logic here
      const result = await this.processNode(context);
      
      // Store in journey context
      this.updateJourneyContext(context, { nodeResult: result });
      
      // Store in global context for session-wide access
      const globalCtx = this.getGlobalContext(context);
      globalCtx.sessionData[`node_${context.coordinates?.nodeData?.id}`] = result;
      
      resolve({
        code: error_codes.success,
        result,
        nodeType: "nodeName"
      });
    } catch (error) {
      resolve({
        code: error_codes.pluginInternalError,
        msg: error.message || error,
      });
    }
  });
}
```

## Form Plugin Enhancement
- Automatically stores user inputs in global context
- Session-wide access to all form data
- Completed forms stored with timestamps

## Benefits
1. **Clean Separation**: Plugin logic in plugins, orchestration in flow-engine
2. **Minimal Code**: Only essential functionality implemented
3. **Global Session Data**: Persistent across journeys
4. **Context Preservation**: Flexible control via preserveContext flag
5. **Automatic Cleanup**: Journey data cleared on completion
6. **Backward Compatible**: Existing flows work without changes

## Usage Example

```json
{
  "process": {
    "flowConfig": {
      "flowId": "next_flow",
      "journeyId": "next_journey", 
      "preserveContext": false,
      "passExistingContext": true,
      "resumeFromThisContext": false
    }
  }
}
```

This implementation provides the required journey context features while maintaining clean architecture and minimal code footprint.