# Journey Context Implementation

## Overview
This implementation provides a comprehensive solution for managing journey context with advanced features including context preservation, global session management, and automatic cleanup.

## Key Features Implemented

### 1. Journey Context Lifecycle Management
- **Creation**: Journey context is created when a journey starts
- **Preservation**: Context can be preserved or cleared at flow connector nodes
- **Cleanup**: Automatic cleanup when journey completes or at specified points

### 2. Flow Connector Context Preservation
- **preserveContext**: New boolean property in flow connector schema
- **Default Behavior**: preserveContext defaults to `true`
- **Cascading Cleanup**: When preserveContext is `false`, all accumulated journey data is cleared
- **Level Tracking**: Journey level tracking for nested flows (up to 10 levels deep)

### 3. Global Session Context
- **Session-wide Storage**: Data persists across multiple journeys in the same session
- **User Input History**: Stores all user inputs with timestamps and node IDs
- **Cross-Journey Access**: Any plugin can read/write to global context

### 4. Enhanced ConvModulePlugin Base Class
New methods added:
- `updateJourneyContext(context, data)`: Update journey-specific context
- `getGlobalContext(context)`: Access global session context
- `updateGlobalContext(context, data)`: Update global session data
- `storeUserInput(context, fieldName, value)`: Store user input globally
- `clearJourneyContext(context, preserveContext)`: Clear journey context
- `handleFlowConnectorContext(context, flowConfig)`: Handle flow connector logic

### 5. New Conversation Nodes Created

#### RequestNode
- **Purpose**: Make HTTP/API requests
- **Features**: Channel-specific configurations, global storage option
- **Schema**: `/convModules/request/`

#### NotificationNode
- **Purpose**: Send notifications
- **Features**: Multi-language support, channel-specific templates
- **Schema**: `/convModules/notification/`

#### InteractiveMessageNode
- **Purpose**: Send interactive messages with buttons
- **Features**: Multi-language buttons, channel-specific layouts
- **Schema**: `/convModules/interactiveMessage/`

#### FeedbackNode
- **Purpose**: Collect user feedback
- **Features**: Customizable forms, rating systems, surveys
- **Schema**: `/convModules/feedback/`

#### ScriptNode
- **Purpose**: Execute custom JavaScript code
- **Features**: Safe execution environment, context access
- **Schema**: `/convModules/script/`

#### PaymentNode
- **Purpose**: Handle payment transactions
- **Features**: Multiple payment providers, transaction tracking
- **Schema**: `/convModules/payment/`

#### LanguageNode
- **Purpose**: Language detection and switching
- **Features**: Auto-detection, language switching, session persistence
- **Schema**: `/convModules/language/`

## Context Structure

### Journey Context
```javascript
{
  nodeId: \"current_node_id\",
  sessionId: \"session_identifier\",
  startTime: \"2024-01-01T00:00:00.000Z\",
  journeyLevel: 1,
  // Journey-specific data
  lastRequest: {...},
  lastResponse: {...},
  awaitingInput: false,
  currentForm: null
}
```

### Global Context
```javascript
{
  sessionData: {
    // Cross-journey persistent data
    selectedLanguage: \"en\",
    userPreferences: {...},
    form_formId: {...}
  },
  userInputHistory: {
    fieldName: {
      value: \"user_input\",
      timestamp: \"2024-01-01T00:00:00.000Z\",
      nodeId: \"node_id\"
    }
  },
  sessionStartTime: \"2024-01-01T00:00:00.000Z\"
}
```

## Usage Examples

### 1. Flow Connector with Context Preservation
```json
{
  \"process\": {
    \"flowConfig\": {
      \"flowId\": \"next_flow_id\",
      \"journeyId\": \"next_journey_id\",
      \"preserveContext\": true,
      \"passExistingContext\": true,
      \"resumeFromThisContext\": false
    }
  }
}
```

### 2. Form Node with Global Storage
```javascript
// Form plugin automatically stores user inputs in global context
// Access previous inputs:
const globalCtx = this.getGlobalContext(context);
const previousName = globalCtx.userInputHistory.name?.value;
```

### 3. Request Node with Channel Configuration
```json
{
  \"process\": {
    \"url\": \"https://api.example.com/data\",
    \"method\": \"POST\",
    \"storeGlobally\": true,
    \"channelConfig\": {
      \"web\": {
        \"url\": \"https://web-api.example.com/data\"
      },
      \"whatsapp\": {
        \"url\": \"https://whatsapp-api.example.com/data\"
      }
    }
  }
}
```

## Context Cleanup Behavior

### Scenario 1: preserveContext = true (Default)
- Journey data flows through all levels
- Context accumulates across flow transitions
- Cleanup only occurs at journey completion

### Scenario 2: preserveContext = false
- Journey context cleared at that flow connector
- Global session data preserved
- Subsequent flows start with clean journey context

### Scenario 3: Journey Completion
- All journey context cleared automatically
- Global session data remains available
- New journeys start fresh but can access global data

## Integration Points

### Bot Interaction Service
- Enhanced `JourneyContext` type with global context support
- Flow engine handles context preservation logic
- Automatic cleanup on journey completion

### App Engine
- SessionManager enhanced with context lifecycle management
- Flow connector handling in app_man.js
- Journey completion detection and cleanup

### Plugin Manager
- All convModules inherit enhanced context capabilities
- Consistent API across all conversation nodes
- Global context access for cross-node data sharing

## Benefits

1. **Flexible Context Management**: Developers can control context preservation at each flow transition
2. **Global Session Storage**: Data persists across journeys for better user experience
3. **Automatic Cleanup**: Prevents memory leaks and ensures clean state management
4. **Multi-language Support**: Built-in support for internationalization
5. **Channel Agnostic**: Same logic works across web, WhatsApp, and other channels
6. **Developer Friendly**: Simple API for context access and manipulation

## Migration Notes

- Existing flow connectors will work with default `preserveContext: true`
- New global context features are opt-in
- Backward compatibility maintained for existing implementations
- Enhanced form plugin automatically stores inputs globally

This implementation provides a robust foundation for complex conversational flows while maintaining simplicity and performance.