#!/bin/bash

# This script runs 'npm install' in all subdirectories of the studio folder
# that contain a package.json file.

STUDIO_DIR="./studio"

echo "Starting npm install for all modules in $STUDIO_DIR..."

# Find all package.json files, excluding node_modules, and run npm install in their directories
find "$STUDIO_DIR" -type f -name "package.json" -not -path "*/node_modules/*" | while read -r pkg_file; do
    dir=$(dirname "$pkg_file")
    echo "--------------------------------------------------"
    echo "Found package.json in: $dir"
    echo "Running 'npm install'..."
    echo "--------------------------------------------------"
    (cd "$dir" && npm install)
done

cd ./studio/utility
npm i -f

echo "--------------------------------------------------"
echo "All installations are complete."
echo "--------------------------------------------------"
