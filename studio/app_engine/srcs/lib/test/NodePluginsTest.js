/**
 * Node Plugins Test
 * 
 * Tests the behavior of feedback, notification, and agent transfer plugins
 * to ensure they work correctly according to requirements.
 */

"use strict";

const NodePlugins = require("../NodePlugins");

class NodePluginsTest {
  
  constructor() {
    this.testResults = [];
  }

  /**
   * Run all plugin tests
   */
  async runAllTests() {
    console.log('🧪 Starting Node Plugins Tests...\n');

    await this.testFeedbackPlugin();
    await this.testNotificationPlugin();
    await this.testAgentTransferPlugin();

    this.printResults();
  }

  /**
   * Test Feedback Plugin
   */
  async testFeedbackPlugin() {
    console.log('📝 Testing Feedback Plugin...');

    // Test 1: Star Rating on Web Channel
    const webSession = this.createMockSession({
      feedbackType: 'star_rating',
      prompt: { en: 'Rate our service', es: 'Califica nuestro servicio' },
      maxStars: 5
    }, 'web', 'en');

    const starRatingResult = NodePlugins.feedback.execute(webSession);
    this.addResult('Feedback - Star Rating on Web', starRatingResult === true);

    // Test 2: Star Rating on WhatsApp (should fail)
    const whatsappSession = this.createMockSession({
      feedbackType: 'star_rating',
      prompt: { en: 'Rate our service' },
      maxStars: 5
    }, 'whatsapp', 'en');

    const whatsappResult = NodePlugins.feedback.execute(whatsappSession);
    this.addResult('Feedback - Star Rating on WhatsApp (should fail)', whatsappResult === false);

    // Test 3: Text Feedback on All Channels
    const textSession = this.createMockSession({
      feedbackType: 'text',
      prompt: { en: 'Please provide feedback' }
    }, 'whatsapp', 'en');

    const textResult = NodePlugins.feedback.execute(textSession);
    this.addResult('Feedback - Text on WhatsApp', textResult === true);

    console.log('✅ Feedback Plugin tests completed\n');
  }

  /**
   * Test Notification Plugin
   */
  async testNotificationPlugin() {
    console.log('📧 Testing Notification Plugin...');

    // Test 1: SMS Notification
    const smsSession = this.createMockSession({
      channels: ['sms'],
      channelConfig: { web: { enabled: true } },
      sms: {
        senderId: 'TESTBOT',
        recipientMsisdn: '+1234567890',
        messageBody: { en: 'Your request processed', es: 'Tu solicitud procesada' }
      }
    }, 'web', 'en');

    const smsResult = NodePlugins.notification.execute(smsSession);
    this.addResult('Notification - SMS', smsResult === true);

    // Test 2: Invalid Email Format
    const invalidEmailSession = this.createMockSession({
      channels: ['email'],
      channelConfig: { web: { enabled: true } },
      email: {
        senderEmail: 'invalid-email',
        recipientEmail: '<EMAIL>',
        subject: { en: 'Test' },
        messageBody: { en: 'Test message' }
      }
    }, 'web', 'en');

    const invalidEmailResult = NodePlugins.notification.execute(invalidEmailSession);
    this.addResult('Notification - Invalid Email (should fail)', invalidEmailResult === false);

    console.log('✅ Notification Plugin tests completed\n');
  }

  /**
   * Test Agent Transfer Plugin
   */
  async testAgentTransferPlugin() {
    console.log('👤 Testing Agent Transfer Plugin...');

    // Test 1: Basic Agent Transfer
    const basicSession = this.createMockSession({
      channels: { web: { enabled: true } },
      transferMessage: { en: 'Connecting to agent', es: 'Conectando con agente' }
    }, 'web', 'en');

    const basicResult = NodePlugins.agentTransfer.execute(basicSession);
    this.addResult('Agent Transfer - Basic Transfer', basicResult === true);

    // Test 2: Channel Not Enabled
    const disabledChannelSession = this.createMockSession({
      channels: { web: { enabled: false } },
      transferMessage: { en: 'Connecting to agent' }
    }, 'web', 'en');

    const disabledResult = NodePlugins.agentTransfer.execute(disabledChannelSession);
    this.addResult('Agent Transfer - Disabled Channel (should fail)', disabledResult === false);

    console.log('✅ Agent Transfer Plugin tests completed\n');
  }

  /**
   * Create mock session for testing
   */
  createMockSession(nodeSettings, channelType = 'web', language = 'en') {
    const mockSession = {
      getContext: () => ({
        getSettings: () => nodeSettings,
        getContextAPI: () => ({
          journey: {
            set: (key, value) => console.log(`Journey set: ${key}`),
            get: (key) => ({ feedbackType: 'star_rating', maxStars: 5 })
          },
          session: {
            set: (key, value) => console.log(`Session set: ${key}`)
          }
        })
      }),
      journeyContext: { channelType, language },
      getLocale: () => language,
      getSessionId: () => 'test-session',
      getAppId: () => 'test-app',
      emit: (event, message, type) => {
        console.log(`📤 ${event}: ${message}`);
        return true;
      }
    };

    return mockSession;
  }

  /**
   * Add test result
   */
  addResult(testName, passed) {
    this.testResults.push({
      name: testName,
      passed: passed,
      status: passed ? '✅' : '❌'
    });
  }

  /**
   * Print test results
   */
  printResults() {
    console.log('📊 Test Results Summary:');
    console.log('========================');
    
    let passedCount = 0;
    let totalCount = this.testResults.length;

    this.testResults.forEach(result => {
      console.log(`${result.status} ${result.name}`);
      if (result.passed) passedCount++;
    });

    console.log('========================');
    console.log(`Total: ${totalCount}, Passed: ${passedCount}, Failed: ${totalCount - passedCount}`);
    console.log(`Success Rate: ${((passedCount / totalCount) * 100).toFixed(1)}%`);

    if (passedCount === totalCount) {
      console.log('🎉 All tests passed!');
    } else {
      console.log('⚠️  Some tests failed. Please review the implementation.');
    }
  }
}

module.exports = NodePluginsTest;