/**
 * Node Plugins for App Engine
 * 
 * Handles feedback, notification, and agent transfer nodes with proper validation
 * and multi-channel/multi-language support within the app engine context.
 */

"use strict";

const ENUMS = require("./enums");

class NodePlugins {
  
  /**
   * Feedback Node Plugin
   */
  static feedback = {
    execute: function(session) {
      const settings = session.getContext().getSettings();
      const process = session.getContext().getProcess();
      const contextAPI = session.getContext().getContextAPI();
      const currentChannel = session.journeyContext.channelType || 'web';
      const currentLanguage = session.getLocale() || 'english';

      try {
        // Validate channel support for feedback type
        if ((settings.feedbackType === 'star_rating' || settings.feedbackType === 'thumbs_up') && 
            currentChannel !== 'web' && currentChannel !== 'mobile') {
          return this.sendChannelError(session, settings.feedbackType, currentChannel);
        }

        // Get channel and language specific content
        const channelData = process[currentChannel];
        if (!channelData) {
          return this.sendError(session, `No configuration found for ${currentChannel} channel`);
        }

        const languageContent = channelData[currentLanguage];
        if (!languageContent || !languageContent.prompt) {
          return this.sendError(session, `No prompt configured for ${currentLanguage} language`);
        }

        // Validate prompt length (max 320 characters)
        if (languageContent.prompt.length > 320) {
          return this.sendError(session, 'Prompt cannot exceed 320 characters');
        }

        // Store feedback configuration in journey context
        contextAPI.journey.set('feedbackConfig', {
          prompt: languageContent.prompt,
          feedbackType: settings.feedbackType,
          maxStars: settings.maxStars || 5,
          channel: currentChannel,
          language: currentLanguage,
          timestamp: new Date().toISOString()
        });

        // Send feedback prompt
        return this.sendFeedbackPrompt(session, settings, languageContent.prompt, currentChannel);

      } catch (error) {
        global.logger.error(`Feedback plugin error: ${error.message}`);
        return this.sendError(session, 'Failed to process feedback node');
      }
    },

    processResponse: function(session, userInput) {
      const contextAPI = session.getContext().getContextAPI();
      const feedbackConfig = contextAPI.journey.get('feedbackConfig');
      
      if (!feedbackConfig) {
        return this.sendError(session, 'No feedback configuration found');
      }

      const validation = this.validateFeedbackResponse(userInput, feedbackConfig);
      if (!validation.isValid) {
        return this.sendError(session, validation.error);
      }

      // Store feedback response
      contextAPI.journey.set('feedbackResponse', {
        response: userInput,
        feedbackType: feedbackConfig.feedbackType,
        timestamp: new Date().toISOString(),
        processed: true
      });

      // Store in session for analytics
      contextAPI.session.set('lastFeedback', {
        response: userInput,
        feedbackType: feedbackConfig.feedbackType,
        timestamp: new Date().toISOString()
      });

      return this.sendThankYouMessage(session, feedbackConfig.language);
    },

    isChannelSupported: function(settings, channel) {
      if (settings.feedbackType === 'star_rating' || settings.feedbackType === 'thumbs_up') {
        return channel === 'web' || channel === 'mobile';
      }
      return true; // Text feedback supported on all channels
    },

    getLocalizedText: function(textObj, language) {
      if (typeof textObj === 'string') return textObj;
      return textObj?.[language] || textObj?.['en'] || null;
    },

    sendFeedbackPrompt: function(session, settings, prompt, channel) {
      let responseMessage = prompt;

      switch (settings.feedbackType) {
        case 'star_rating':
          if (channel === 'web' || channel === 'mobile') {
            responseMessage += `\n⭐ Please rate from 1 to ${settings.maxStars || 5} stars`;
          }
          break;
        
        case 'thumbs_up':
          if (channel === 'web' || channel === 'mobile') {
            responseMessage += '\n👍 Thumbs up or 👎 Thumbs down?';
          }
          break;
        
        case 'text':
          responseMessage += '\nPlease provide your feedback:';
          break;
      }

      session.emit('MENU', responseMessage, 'USSD');
      return true;
    },

    validateFeedbackResponse: function(userInput, config) {
      switch (config.feedbackType) {
        case 'star_rating':
          const rating = parseInt(userInput);
          if (isNaN(rating) || rating < 1 || rating > (config.maxStars || 5)) {
            return {
              isValid: false,
              error: `Please provide a rating between 1 and ${config.maxStars || 5}`
            };
          }
          break;

        case 'thumbs_up':
          const normalizedInput = userInput.toLowerCase();
          if (!['up', 'down', 'thumbs up', 'thumbs down', '👍', '👎'].includes(normalizedInput)) {
            return {
              isValid: false,
              error: 'Please respond with thumbs up or thumbs down'
            };
          }
          break;

        case 'text':
          if (!userInput || userInput.trim().length === 0) {
            return {
              isValid: false,
              error: 'Please provide your feedback'
            };
          }
          break;
      }

      return { isValid: true };
    },

    sendChannelError: function(session, feedbackType, channel) {
      const message = `${feedbackType} feedback is not supported on ${channel} channel. Please use web or mobile.`;
      session.emit('RESPONSE', message, 'USSD');
      return false;
    },

    sendError: function(session, message) {
      session.emit('RESPONSE', message, 'USSD');
      return false;
    },

    sendThankYouMessage: function(session, language) {
      const thankYouMessages = {
        'en': 'Thank you for your feedback!',
        'es': '¡Gracias por tu comentario!',
        'fr': 'Merci pour votre retour!'
      };
      
      const message = thankYouMessages[language] || thankYouMessages['en'];
      session.emit('RESPONSE', message, 'USSD');
      return true;
    }
  },

  /**
   * Notification Node Plugin
   */
  static notification = {
    execute: function(session) {
      const settings = session.getContext().getSettings();
      const process = session.getContext().getProcess();
      const contextAPI = session.getContext().getContextAPI();
      const currentChannel = session.journeyContext.channelType || 'web';
      const currentLanguage = session.getLocale() || 'english';

      try {
        // Validate channels selection
        if (!settings.channels || settings.channels.length === 0) {
          return this.sendError(session, 'At least one notification channel must be selected');
        }

        // Get channel and language specific content
        const channelData = process[currentChannel];
        if (!channelData) {
          return this.sendError(session, `No configuration found for ${currentChannel} channel`);
        }

        const languageContent = channelData[currentLanguage];
        if (!languageContent) {
          return this.sendError(session, `No content configured for ${currentLanguage} language`);
        }

        // Validate SMS if selected
        if (settings.channels.includes('sms') || settings.channels.includes('both')) {
          const smsValidation = this.validateSMS(settings.sms, languageContent);
          if (!smsValidation.isValid) {
            return this.sendError(session, smsValidation.error);
          }
        }

        // Validate Email if selected
        if (settings.channels.includes('email') || settings.channels.includes('both')) {
          const emailValidation = this.validateEmail(settings.email, languageContent);
          if (!emailValidation.isValid) {
            return this.sendError(session, emailValidation.error);
          }
        }

        // Store notification configuration
        const notificationConfig = {
          channels: settings.channels,
          sms: settings.sms,
          email: settings.email,
          content: languageContent,
          timestamp: new Date().toISOString(),
          status: 'pending',
          channel: currentChannel,
          language: currentLanguage
        };

        contextAPI.journey.set('notificationConfig', notificationConfig);

        // Trigger notification processing
        this.processNotification(session, notificationConfig);

        session.emit('RESPONSE', 'Notification sent successfully', 'USSD');
        return true;

      } catch (error) {
        global.logger.error(`Notification plugin error: ${error.message}`);
        return this.sendError(session, 'Failed to process notification');
      }
    },

    validateSMS: function(smsConfig, languageContent) {
      if (!smsConfig) {
        return { isValid: false, error: 'SMS configuration is required' };
      }

      if (!smsConfig.senderId) {
        return { isValid: false, error: 'SMS sender ID is required' };
      }

      if (!smsConfig.recipientMsisdn) {
        return { isValid: false, error: 'SMS recipient MSISDN is required' };
      }

      if (!languageContent.smsMessageBody || languageContent.smsMessageBody.length > 320) {
        return { isValid: false, error: 'SMS message body is required and cannot exceed 320 characters' };
      }

      return { isValid: true };
    },

    validateEmail: function(emailConfig, languageContent) {
      if (!emailConfig) {
        return { isValid: false, error: 'Email configuration is required' };
      }

      if (!emailConfig.senderEmail || !this.isValidEmail(emailConfig.senderEmail)) {
        return { isValid: false, error: 'Valid sender email is required' };
      }

      if (!emailConfig.recipientEmail || !this.isValidEmail(emailConfig.recipientEmail)) {
        return { isValid: false, error: 'Valid recipient email is required' };
      }

      if (!languageContent.emailSubject) {
        return { isValid: false, error: 'Email subject is required' };
      }

      if (!languageContent.emailMessageBody || languageContent.emailMessageBody.length > 320) {
        return { isValid: false, error: 'Email message body is required and cannot exceed 320 characters' };
      }

      return { isValid: true };
    },

    processMultiLanguageContent: function(config, language) {
      const processed = { ...config };
      
      if (config.messageBody) {
        processed.messageBody = this.getLocalizedText(config.messageBody, language);
      }
      
      if (config.subject) {
        processed.subject = this.getLocalizedText(config.subject, language);
      }
      
      return processed;
    },

    getLocalizedText: function(textObj, language) {
      if (typeof textObj === 'string') return textObj;
      return textObj?.[language] || textObj?.['en'] || null;
    },

    isValidEmail: function(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },

    processNotification: function(session, config) {
      // This would integrate with actual notification service
      global.logger.info(`Processing notification: ${JSON.stringify(config)}`);
      
      // Store in session for tracking
      const contextAPI = session.getContext().getContextAPI();
      contextAPI.session.set('lastNotification', {
        channels: config.channels,
        timestamp: config.timestamp,
        status: 'sent'
      });
    },

    sendError: function(session, message) {
      session.emit('RESPONSE', message, 'USSD');
      return false;
    }
  },

  /**
   * Agent Transfer Node Plugin
   */
  static agentTransfer = {
    execute: function(session) {
      const contextAPI = session.getContext().getContextAPI();
      const currentChannel = session.journeyContext.channelType || 'web';
      const currentLanguage = session.getLocale() || 'english';

      try {
        // Agent transfer node has no configuration fields as per requirements
        // It uses configuration from 'Agent Transfer' page
        
        const transferMessage = 'Connecting you to a live agent...';

        // Store agent transfer request in session context
        contextAPI.session.set('agentTransferRequested', {
          timestamp: new Date().toISOString(),
          journeyId: session.getAppId(),
          transferMessage,
          channel: currentChannel,
          language: currentLanguage,
          note: 'Integrate Agent from the Transfer Agent page to configure the Native Agent'
        });

        // Send transfer message
        session.emit('RESPONSE', transferMessage, 'USSD');

        global.logger.info(`Agent transfer requested for session ${session.getSessionId()}`);
        return true;

      } catch (error) {
        global.logger.error(`Agent transfer plugin error: ${error.message}`);
        return this.sendError(session, 'Failed to process agent transfer');
      }
    },

    getLocalizedText: function(textObj, language) {
      if (typeof textObj === 'string') return textObj;
      return textObj?.[language] || textObj?.['en'] || null;
    },

    sendError: function(session, message) {
      session.emit('RESPONSE', message, 'USSD');
      return false;
    }
  }
}

module.exports = NodePlugins;