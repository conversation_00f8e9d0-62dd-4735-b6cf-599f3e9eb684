"use strict";
/**
 *  Agent Transfer Node Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class AgentTransferPlugin
 */
class AgentTransferPlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        // Agent transfer node has no configuration fields as per requirements
        // It uses configuration from 'Agent Transfer' page
        
        const transferMessage = 'Connecting you to a live agent...';
        const currentChannel = context.channelType || 'web';
        const currentLanguage = context.language || 'english';

        // Store agent transfer request in journey context
        this.updateJourneyContext(context, {
          agentTransferRequested: {
            timestamp: new Date().toISOString(),
            journeyId: context.appId,
            transferMessage,
            channel: currentChannel,
            language: currentLanguage,
            note: 'Integrate Agent from the Transfer Agent page to configure the Native Agent'
          }
        });

        // Store in session context for tracking
        this.updateSessionContext(context, {
          agentTransferRequested: {
            timestamp: new Date().toISOString(),
            journeyId: context.appId,
            transferMessage,
            channel: currentChannel,
            language: currentLanguage
          }
        });

        resolve({
          code: error_codes.success,
          message: transferMessage,
          nodeType: "agentTransfer"
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  close() {}
}

module.exports = AgentTransferPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./agentTransferSchema.json");
  schema.category = "agentTransfer";
  return schema;
}