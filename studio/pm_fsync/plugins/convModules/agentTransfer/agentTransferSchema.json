{"typeId": "2.9", "name": "AgentTransfer", "title": "Agent Transfer", "description": "Transfer conversation to live agent", "type": "object", "required": ["name", "coordinates", "settings", "output"], "properties": {"name": {"description": "Name of the module", "title": "Module name", "type": "string", "minLength": 1, "maxLength": 50}, "coordinates": {"description": "Coordinates of the module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties of the module", "title": "Settings", "type": "object", "properties": {"note": {"description": "Configuration note", "title": "Configuration Note", "type": "string", "default": "Integrate Agent from the 'Transfer Agent' page to configure the Native Agent.", "readOnly": true}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "output": {"description": "The output params", "type": "object"}}}