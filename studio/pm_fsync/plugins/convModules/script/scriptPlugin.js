"use strict";
/**
 *  Script Node Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class ScriptPlugin
 */
class ScriptPlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        const { scriptCode, scriptType, parameters } = context.process;
        
        // Create execution context for script
        const scriptContext = {
          journeyContext: context.journeyContext,
          globalContext: this.getGlobalContext(context),
          parameters: parameters || {},
          nodeId: context.coordinates?.nodeData?.id,
          channelType: context.channelType,
          language: context.language
        };

        let result;
        
        if (scriptType === 'javascript') {
          // Execute JavaScript code
          result = this.executeJavaScript(scriptCode, scriptContext);
        } else {
          throw new Error(`Unsupported script type: ${scriptType}`);
        }

        // Store script result in journey context
        this.updateJourneyContext(context, {
          lastScriptResult: result,
          scriptExecutionTime: new Date().toISOString()
        });

        // Store in global context
        const globalCtx = this.getGlobalContext(context);
        globalCtx.sessionData[`script_${context.coordinates?.nodeData?.id}`] = result;

        resolve({
          code: error_codes.success,
          result,
          nodeType: "script"
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  executeJavaScript(code, context) {
    try {
      // Create a safe execution environment
      const safeContext = {
        context,
        console: {
          log: (...args) => global.logger.info(...args),
          error: (...args) => global.logger.error(...args)
        },
        Math,
        Date,
        JSON,
        String,
        Number,
        Boolean,
        Array,
        Object
      };

      // Wrap code in a function to control scope
      const wrappedCode = `
        (function(context, console, Math, Date, JSON, String, Number, Boolean, Array, Object) {
          ${code}
        })
      `;

      const func = eval(wrappedCode);
      return func(
        safeContext.context,
        safeContext.console,
        safeContext.Math,
        safeContext.Date,
        safeContext.JSON,
        safeContext.String,
        safeContext.Number,
        safeContext.Boolean,
        safeContext.Array,
        safeContext.Object
      );
    } catch (error) {
      throw new Error(`Script execution failed: ${error.message}`);
    }
  }

  close() {}
}

module.exports = ScriptPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./scriptSchema.json");
  schema.category = "script";
  return schema;
}