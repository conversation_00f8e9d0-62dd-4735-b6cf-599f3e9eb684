{"typeId": "2.8", "name": "<PERSON><PERSON><PERSON>", "title": "Script Execution", "description": "Execute custom JavaScript code with access to context data", "type": "object", "required": ["name", "coordinates", "process", "output"], "properties": {"name": {"description": "Name of the module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates of the module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "process": {"description": "Processing properties of the module", "title": "Process", "type": "object", "required": ["scriptCode", "scriptType"], "properties": {"scriptType": {"description": "Type of script", "title": "Script Type", "type": "string", "enum": ["javascript"], "default": "javascript"}, "scriptCode": {"description": "Script code to execute", "title": "Script Code", "type": "string"}, "parameters": {"description": "Parameters to pass to script", "title": "Parameters", "type": "object"}, "storeGlobally": {"description": "Store script result in global session context", "title": "Store Globally", "type": "boolean", "default": false}}}, "output": {"description": "The output params", "type": "object"}}}