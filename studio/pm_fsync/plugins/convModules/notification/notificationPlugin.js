"use strict";
/**
 *  Notification Node Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class NotificationPlugin
 */
class NotificationPlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        const { notificationType, content, channelConfig, multiLanguage } = context.process;
        const channelType = context.channelType || 'web';
        const language = context.language || 'en';
        
        // Get channel-specific configuration
        const channelNotificationConfig = channelConfig?.[channelType] || {};
        
        // Get language-specific content
        const notificationContent = multiLanguage?.[language] || content;
        
        // Build notification payload
        const notification = {
          type: channelNotificationConfig.type || notificationType,
          title: notificationContent.title,
          message: notificationContent.message,
          data: notificationContent.data || {},
          channel: channelType,
          timestamp: new Date().toISOString()
        };

        // Store notification in journey context
        this.updateJourneyContext(context, {
          lastNotification: notification
        });

        // Store in global context
        const globalCtx = this.getGlobalContext(context);
        globalCtx.sessionData[`notification_${context.coordinates?.nodeData?.id}`] = notification;

        resolve({
          code: error_codes.success,
          notification,
          nodeType: "notification"
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  close() {}
}

module.exports = NotificationPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./notificationSchema.json");
  schema.category = "notification";
  return schema;
}