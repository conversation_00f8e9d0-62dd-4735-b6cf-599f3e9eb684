{"typeId": "2.5", "name": "Notification", "title": "Notification", "description": "Send notifications via SMS, Email or both channels", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name of the module", "title": "Module name", "type": "string", "minLength": 1, "maxLength": 50}, "coordinates": {"description": "Coordinates of the module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties of the module", "title": "Settings", "type": "object", "properties": {"channels": {"description": "Notification channels", "title": "Notification Channels", "type": "array", "items": {"type": "string", "enum": ["sms", "email", "both"]}, "minItems": 1}, "sms": {"description": "SMS configuration", "title": "SMS Configuration", "type": "object", "properties": {"senderId": {"type": "string", "description": "SMS sender ID"}, "recipientMsisdn": {"type": "string", "description": "Recipient MSISDN"}}}, "email": {"description": "Email configuration", "title": "Email Configuration", "type": "object", "properties": {"senderEmail": {"type": "string", "format": "email", "description": "Sender email address"}, "recipientEmail": {"type": "string", "format": "email", "description": "Recipient email address"}}}}, "required": ["channels"]}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"type": "object", "properties": {"web": {"type": "object", "properties": {"english": {"$ref": "#/definitions/notificationContent"}, "hindi": {"$ref": "#/definitions/notificationContent"}, "german": {"$ref": "#/definitions/notificationContent"}, "arabic": {"$ref": "#/definitions/notificationContent"}}, "required": ["english", "hindi", "german", "arabic"], "additionalProperties": false}, "mobile": {"type": "object", "properties": {"english": {"$ref": "#/definitions/notificationContent"}, "hindi": {"$ref": "#/definitions/notificationContent"}, "german": {"$ref": "#/definitions/notificationContent"}, "arabic": {"$ref": "#/definitions/notificationContent"}}, "required": ["english", "hindi", "german", "arabic"], "additionalProperties": false}, "whatsapp": {"type": "object", "properties": {"english": {"$ref": "#/definitions/notificationContent"}, "hindi": {"$ref": "#/definitions/notificationContent"}, "german": {"$ref": "#/definitions/notificationContent"}, "arabic": {"$ref": "#/definitions/notificationContent"}}, "required": ["english", "hindi", "german", "arabic"], "additionalProperties": false}}, "required": ["web", "mobile", "whatsapp"], "additionalProperties": false}, "output": {"description": "The output params", "type": "object"}, "definitions": {"notificationContent": {"type": "object", "properties": {"smsMessageBody": {"type": "string", "maxLength": 320, "description": "SMS message body"}, "emailSubject": {"type": "string", "description": "Email subject"}, "emailMessageBody": {"type": "string", "maxLength": 320, "description": "Email message body"}}, "additionalProperties": false}}}}