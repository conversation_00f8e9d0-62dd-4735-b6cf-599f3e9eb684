"use strict";
/**
 *  Payment Node Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class PaymentPlugin
 */
class PaymentPlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        const { paymentProvider, amount, currency, description, channelConfig } = context.process;
        const channelType = context.channelType || 'web';
        
        // Get channel-specific configuration
        const channelPaymentConfig = channelConfig?.[channelType] || {};
        
        // Build payment request
        const paymentRequest = {
          provider: channelPaymentConfig.provider || paymentProvider,
          amount: channelPaymentConfig.amount || amount,
          currency: channelPaymentConfig.currency || currency,
          description: channelPaymentConfig.description || description,
          channel: channelType,
          timestamp: new Date().toISOString(),
          transactionId: this.generateTransactionId()
        };

        // Store payment request in journey context
        this.updateJourneyContext(context, {
          activePaymentRequest: paymentRequest,
          awaitingPayment: true
        });

        // Store in global context
        const globalCtx = this.getGlobalContext(context);
        globalCtx.sessionData[`payment_${context.coordinates?.nodeData?.id}`] = paymentRequest;
        if (!globalCtx.sessionData.paymentHistory) {
          globalCtx.sessionData.paymentHistory = [];
        }
        globalCtx.sessionData.paymentHistory.push(paymentRequest);

        resolve({
          code: error_codes.success,
          paymentRequest,
          nodeType: "payment"
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  generateTransactionId() {
    return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  close() {}
}

module.exports = PaymentPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./paymentSchema.json");
  schema.category = "payment";
  return schema;
}