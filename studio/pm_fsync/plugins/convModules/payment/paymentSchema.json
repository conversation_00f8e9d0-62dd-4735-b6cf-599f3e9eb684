{"typeId": "2.9", "name": "Payment", "title": "Payment", "description": "Handle payment transactions with multiple providers", "type": "object", "required": ["name", "coordinates", "process", "output"], "properties": {"name": {"description": "Name of the module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates of the module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "process": {"description": "Processing properties of the module", "title": "Process", "type": "object", "required": ["paymentProvider", "amount", "currency"], "properties": {"paymentProvider": {"description": "Payment provider", "title": "Payment Provider", "type": "string", "enum": ["stripe", "paypal", "razorpay", "square"], "default": "stripe"}, "amount": {"description": "Payment amount", "title": "Amount", "type": "number", "minimum": 0}, "currency": {"description": "Payment currency", "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "enum": ["USD", "EUR", "GBP", "INR", "JPY"], "default": "USD"}, "description": {"description": "Payment description", "title": "Description", "type": "string"}, "channelConfig": {"description": "Channel-specific configurations", "title": "Channel Configuration", "type": "object", "properties": {"web": {"type": "object", "properties": {"provider": {"type": "string"}, "amount": {"type": "number"}, "currency": {"type": "string"}, "description": {"type": "string"}}}, "whatsapp": {"type": "object", "properties": {"provider": {"type": "string"}, "amount": {"type": "number"}, "currency": {"type": "string"}, "description": {"type": "string"}}}}}}}, "output": {"description": "The output params", "type": "object"}}}