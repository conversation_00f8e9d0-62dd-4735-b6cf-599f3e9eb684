"use strict";
/**
 *  Interactive Message Node Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class InteractiveMessagePlugin
 */
class InteractiveMessagePlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        const { messageType, content, buttons, multiLanguage, channelConfig } = context.process;
        const channelType = context.channelType || 'web';
        const language = context.language || 'en';
        
        // Get channel-specific configuration
        const channelMessageConfig = channelConfig?.[channelType] || {};
        
        // Get language-specific content
        const messageContent = multiLanguage?.[language] || content;
        const messageButtons = multiLanguage?.[language]?.buttons || buttons;
        
        // Build interactive message
        const interactiveMessage = {
          type: channelMessageConfig.type || messageType,
          text: messageContent.text,
          buttons: messageButtons?.map(btn => ({
            id: btn.id,
            title: btn.title,
            payload: btn.payload
          })) || [],
          channel: channelType,
          timestamp: new Date().toISOString()
        };

        // Store message in journey context
        this.updateJourneyContext(context, {
          lastInteractiveMessage: interactiveMessage,
          awaitingUserSelection: true
        });

        // Store in global context
        const globalCtx = this.getGlobalContext(context);
        globalCtx.sessionData[`interactive_${context.coordinates?.nodeData?.id}`] = interactiveMessage;

        resolve({
          code: error_codes.success,
          message: interactiveMessage,
          nodeType: "interactiveMessage"
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  close() {}
}

module.exports = InteractiveMessagePlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./interactiveMessageSchema.json");
  schema.category = "interactiveMessage";
  return schema;
}