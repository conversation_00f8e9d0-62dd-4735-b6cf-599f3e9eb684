{"typeId": "2.6", "name": "InteractiveMessage", "title": "Interactive Message", "description": "Send interactive messages with buttons and multi-language support", "type": "object", "required": ["name", "coordinates", "process", "output"], "properties": {"name": {"description": "Name of the module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates of the module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "process": {"description": "Processing properties of the module", "title": "Process", "type": "object", "required": ["messageType", "content"], "properties": {"messageType": {"description": "Type of interactive message", "title": "Message Type", "type": "string", "enum": ["button", "list", "carousel"], "default": "button"}, "content": {"description": "Default message content", "title": "Content", "type": "object", "properties": {"text": {"type": "string", "description": "Message text"}}}, "buttons": {"description": "Interactive buttons", "title": "Buttons", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "payload": {"type": "string"}}}}, "multiLanguage": {"description": "Multi-language content", "title": "Multi-Language Content", "type": "object", "properties": {"en": {"type": "object", "properties": {"text": {"type": "string"}, "buttons": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "payload": {"type": "string"}}}}}}, "es": {"type": "object", "properties": {"text": {"type": "string"}, "buttons": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "payload": {"type": "string"}}}}}}}}, "channelConfig": {"description": "Channel-specific configurations", "title": "Channel Configuration", "type": "object", "properties": {"web": {"type": "object", "properties": {"type": {"type": "string"}, "layout": {"type": "string"}}}, "whatsapp": {"type": "object", "properties": {"type": {"type": "string"}, "template": {"type": "string"}}}}}, "storeGlobally": {"description": "Store message in global session context", "title": "Store Globally", "type": "boolean", "default": false}}}, "output": {"description": "The output params", "type": "object"}}}