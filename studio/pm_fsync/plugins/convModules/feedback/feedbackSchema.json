{"typeId": "2.7", "name": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "description": "Collect user feedback with star rating, thumbs up/down, or text input", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name of the module", "title": "Module name", "type": "string", "minLength": 1, "maxLength": 50}, "coordinates": {"description": "Coordinates of the module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties of the module", "title": "Settings", "type": "object", "properties": {"feedbackType": {"description": "Type of feedback to collect", "title": "Feedback Type", "type": "string", "enum": ["star_rating", "thumbs_up", "text"], "default": "star_rating"}, "maxStars": {"description": "Maximum number of stars for rating (1-5)", "title": "Maximum Stars", "type": "integer", "minimum": 1, "maximum": 5, "default": 5}}, "required": ["feedbackType"]}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"type": "object", "properties": {"web": {"type": "object", "properties": {"english": {"$ref": "#/definitions/feedbackContent"}, "hindi": {"$ref": "#/definitions/feedbackContent"}, "german": {"$ref": "#/definitions/feedbackContent"}, "arabic": {"$ref": "#/definitions/feedbackContent"}}, "required": ["english", "hindi", "german", "arabic"], "additionalProperties": false}, "mobile": {"type": "object", "properties": {"english": {"$ref": "#/definitions/feedbackContent"}, "hindi": {"$ref": "#/definitions/feedbackContent"}, "german": {"$ref": "#/definitions/feedbackContent"}, "arabic": {"$ref": "#/definitions/feedbackContent"}}, "required": ["english", "hindi", "german", "arabic"], "additionalProperties": false}, "whatsapp": {"type": "object", "properties": {"english": {"$ref": "#/definitions/feedbackContent"}, "hindi": {"$ref": "#/definitions/feedbackContent"}, "german": {"$ref": "#/definitions/feedbackContent"}, "arabic": {"$ref": "#/definitions/feedbackContent"}}, "required": ["english", "hindi", "german", "arabic"], "additionalProperties": false}}, "required": ["web", "mobile", "whatsapp"], "additionalProperties": false}, "output": {"description": "The output params", "type": "object"}, "definitions": {"feedbackContent": {"type": "object", "properties": {"prompt": {"type": "string", "minLength": 1, "maxLength": 320, "description": "Prompt asked by chatbot"}}, "required": ["prompt"], "additionalProperties": false}}}}