"use strict";
/**
 *  Feedback Node Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class FeedbackPlugin
 */
class FeedbackPlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        const { feedbackType, questions, multiLanguage, channelConfig } = context.process;
        const channelType = context.channelType || 'web';
        const language = context.language || 'en';
        
        // Get channel-specific configuration
        const channelFeedbackConfig = channelConfig?.[channelType] || {};
        
        // Get language-specific content
        const feedbackQuestions = multiLanguage?.[language]?.questions || questions;
        
        // Build feedback form
        const feedbackForm = {
          type: channelFeedbackConfig.type || feedbackType,
          questions: feedbackQuestions?.map(q => ({
            id: q.id,
            text: q.text,
            type: q.type,
            options: q.options || [],
            required: q.required || false
          })) || [],
          channel: channelType,
          timestamp: new Date().toISOString()
        };

        // Store feedback form in journey context
        this.updateJourneyContext(context, {
          activeFeedbackForm: feedbackForm,
          awaitingFeedback: true
        });

        // Store in global context
        const globalCtx = this.getGlobalContext(context);
        globalCtx.sessionData[`feedback_${context.coordinates?.nodeData?.id}`] = feedbackForm;

        resolve({
          code: error_codes.success,
          feedbackForm,
          nodeType: "feedback"
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  /**
   * Process user feedback response
   */
  processFeedbackResponse(context, userInput) {
    return new Promise((resolve) => {
      try {
        const feedbackConfig = this.getJourneyContext(context, 'feedbackConfig');
        
        if (!feedbackConfig) {
          resolve({
            code: error_codes.contextError,
            msg: 'No feedback configuration found'
          });
          return;
        }

        // Validate response based on feedback type
        const validation = this.validateFeedbackResponse(userInput, feedbackConfig);
        if (!validation.isValid) {
          resolve({
            code: error_codes.validationError,
            msg: validation.error
          });
          return;
        }

        // Store feedback response
        this.updateJourneyContext(context, {
          feedbackResponse: {
            response: userInput,
            feedbackType: feedbackConfig.feedbackType,
            timestamp: new Date().toISOString(),
            processed: true
          },
          awaitingInput: false
        });

        // Store in session for analytics
        this.updateSessionContext(context, {
          lastFeedback: {
            response: userInput,
            feedbackType: feedbackConfig.feedbackType,
            timestamp: new Date().toISOString()
          }
        });

        resolve({
          code: error_codes.success,
          message: 'Thank you for your feedback!'
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error
        });
      }
    });
  }

  /**
   * Validate feedback response based on type
   */
  validateFeedbackResponse(userInput, config) {
    switch (config.feedbackType) {
      case 'star_rating':
        const rating = parseInt(userInput);
        if (isNaN(rating) || rating < 1 || rating > (config.maxStars || 5)) {
          return {
            isValid: false,
            error: `Please provide a rating between 1 and ${config.maxStars || 5}`
          };
        }
        break;

      case 'thumbs_up':
        const normalizedInput = userInput.toLowerCase();
        if (!['up', 'down', 'thumbs up', 'thumbs down', '👍', '👎'].includes(normalizedInput)) {
          return {
            isValid: false,
            error: 'Please respond with thumbs up or thumbs down'
          };
        }
        break;

      case 'text':
        if (!userInput || userInput.trim().length === 0) {
          return {
            isValid: false,
            error: 'Please provide your feedback'
          };
        }
        break;
    }

    return { isValid: true };
  }

  close() {}
}

module.exports = FeedbackPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./feedbackSchema.json");
  schema.category = "feedback";
  return schema;
}