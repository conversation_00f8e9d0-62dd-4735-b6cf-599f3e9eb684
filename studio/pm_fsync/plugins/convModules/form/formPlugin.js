"use strict";
/**
 *  Form Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class FormPlugin
 */
class FormPlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        // Initialize journey context
        this.updateJourneyContext(context);
        const { journeyContext, process } = context;

        // Handle user input if this is a resumed execution
        if (journeyContext?.userMessage) {
          this.handleUserInput(context, journeyContext.userMessage);
        }

        const { channelType, language } = journeyContext;
        const channelData = process[channelType] || process.web;
        const languageContent = channelData[language] || channelData.english;

        const formId = process.formId;
        const formConfig = languageContent.formConfig;

        const isSingleAskForm = formConfig.formType === "single-ask-form";
        const userInput = journeyContext?.formData?.[formId] || {};
        const fieldsLength = isSingleAskForm ? 1 : 0;
        const currentFields = this.getAllPendingFields(formConfig, userInput, fieldsLength);

        if (currentFields.length) {
          // Need more input - pause execution
          const prompt = currentFields.map((currentField) => ({
            fieldName: currentField.name,
            fieldType: currentField.type,
            label: currentField.label,
            required: currentField.required,
            options: currentField.options,
            nodeData: currentField.nodeData || {},
            formId,
            value: userInput[currentField], //TODO: might need to remove this
          }));
          const formNodeData = {
            prompt,
            formId,
            formType: formConfig.formType,
          };
          const result = {
            code: error_codes.success,
            awaitingInput: true,
            nodeType: "form",
            data: formNodeData,
            journeyContext,
          };

          // Update journey context
          this.updateJourneyContext(context, {
            awaitingInput: true,
            currentForm: formNodeData,
          });

          resolve(result);
        } else {
          // All fields collected - form complete
          const result = {
            code: error_codes.success,
            formComplete: true,
            data: {
              formId: formId,
              submittedData: userInput,
              submissionTime: new Date().toISOString(),
            },
          };

          // Update journey context
          this.updateJourneyContext(context, {
            awaitingInput: false,
            currentForm: null,
          });

          // Store completed form data in global context
          const globalCtx = this.getGlobalContext(context);
          globalCtx.sessionData[`form_${formId}`] = {
            data: userInput,
            completedAt: new Date().toISOString(),
          };

          resolve(result);
        }
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  getCurrentField(formConfig, userInput) {
    if (!formConfig?.fields) return null;

    for (const field of formConfig.fields) {
      if (!userInput.hasOwnProperty(field.name)) {
        return field;
      }
    }
    return null;
  }

  /**
   * Returns all pending fields of a form, given the current user input
   * @param {object} formConfig Form configuration
   * @param {object} userInput Current user input
   * @param {number} [fieldsLength] Optional length of fields to return, if passed any falsy value, it will return all pending fields
   * @returns {array} Array of pending form fields
   */
  getAllPendingFields(formConfig, userInput, fieldsLength) {
    const fields = [];
    if (!formConfig?.fields) return fields;

    for (const field of formConfig.fields) {
      if (!userInput.hasOwnProperty(field.name)) {
        fields.push(field);
        if (fieldsLength && fields.length === fieldsLength) return fields;
      }
    }
    return fields;
  }

  close() {}

  /**
   * Handle user input for resumed execution
   * @param {any} context Execution context
   * @param {any} userInput User input data
   */
  handleUserInput(context, userMessage) {
    if (!context.journeyContext) {
      context.journeyContext = {};
    }
    const { journeyContext } = context;

    const { formData, currentForm, awaitingInput } = journeyContext;
    if (awaitingInput && currentForm) {
      const isSingleAskForm = currentForm.formType === "single-ask-form";

      let userFormData = {};
      if (isSingleAskForm) {
        const fieldName = currentForm.prompt[0].fieldName;
        const fieldType = currentForm.prompt[0].fieldType;
        const nodeData = currentForm.prompt[0].nodeData;

        // Validate user input
        const validationResult = this.validateFieldInput(userMessage.content, fieldType, nodeData);
        if (!validationResult.isValid) {
          throw new Error(`Validation failed: ${validationResult.error}`);
        }

        userFormData = {
          [fieldName]: userMessage.content,
        };
      } else {
        // Validate multi-field form data
        for (const [fieldName, value] of Object.entries(userMessage.formData || {})) {
          const field = currentForm.prompt.find((f) => f.fieldName === fieldName);
          if (field) {
            const validationResult = this.validateFieldInput(
              value,
              field.fieldType,
              field.nodeData,
            );
            if (!validationResult.isValid) {
              throw new Error(`Validation failed for ${fieldName}: ${validationResult.error}`);
            }
          }
        }
        userFormData = userMessage.formData;
      }

      // Update journey context form data
      journeyContext.formData = {
        ...formData,
        [currentForm.formId]: {
          ...formData?.[currentForm.formId],
          ...userFormData,
        },
      };

      // Store user input in global context
      Object.keys(userFormData).forEach((fieldName) => {
        this.storeUserInput(context, fieldName, userFormData[fieldName]);
      });
    }
  }

  /**
   * Validate field input based on type and nodeData
   * @param {any} value User input value
   * @param {string} fieldType Field type
   * @param {object} nodeData Additional field configuration
   * @returns {object} Validation result
   */
  validateFieldInput(value, fieldType, nodeData = {}) {
    switch (fieldType) {
      case "text":
        return this.validateTextInput(value, nodeData);
      case "number":
        return this.validateNumberInput(value, nodeData);
      case "email":
        return this.validateEmailInput(value);
      case "select":
        return this.validateSelectInput(value, nodeData);
      case "radio":
      case "checkbox":
        return this.validateBooleanInput(value);
      case "date":
      case "futureDate":
      case "pastDate":
      case "customDate":
        return this.validateDateInput(value, fieldType, nodeData);
      case "Phone number":
        return this.validatePhoneInput(value);
      case "time":
        return this.validateTimeInput(value);
      default:
        return { isValid: true };
    }
  }

  validateTextInput(value, nodeData) {
    if (typeof value !== "string") return { isValid: false, error: "Must be text" };
    if (nodeData.minLength && value.length < nodeData.minLength) {
      return { isValid: false, error: `Minimum length is ${nodeData.minLength}` };
    }
    if (nodeData.maxLength && value.length > nodeData.maxLength) {
      return { isValid: false, error: `Maximum length is ${nodeData.maxLength}` };
    }
    if (nodeData.pattern && !new RegExp(nodeData.pattern).test(value)) {
      return { isValid: false, error: "Invalid format" };
    }
    return { isValid: true };
  }

  validateNumberInput(value, nodeData) {
    const num = Number(value);
    if (isNaN(num)) return { isValid: false, error: "Must be a number" };
    if (nodeData.min !== undefined && num < nodeData.min) {
      return { isValid: false, error: `Minimum value is ${nodeData.min}` };
    }
    if (nodeData.max !== undefined && num > nodeData.max) {
      return { isValid: false, error: `Maximum value is ${nodeData.max}` };
    }
    return { isValid: true };
  }

  validateEmailInput(value) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value)
      ? { isValid: true }
      : { isValid: false, error: "Invalid email format" };
  }

  validateSelectInput(value, nodeData) {
    if (!nodeData.options || !Array.isArray(nodeData.options)) {
      return { isValid: false, error: "No options defined" };
    }
    if (nodeData.multiple) {
      const values = Array.isArray(value) ? value : [value];
      const invalidValues = values.filter((v) => !nodeData.options.includes(v));
      return invalidValues.length === 0
        ? { isValid: true }
        : { isValid: false, error: "Invalid selection" };
    }
    return nodeData.options.includes(value)
      ? { isValid: true }
      : { isValid: false, error: "Invalid selection" };
  }

  validateBooleanInput(value) {
    if (["true", "false"].includes(value)) return { isValid: true };
    return { isValid: false, error: "Invalid selection" };
  }

  validateDateInput(value, fieldType, nodeData) {
    const date = new Date(value);
    if (isNaN(date.getTime())) return { isValid: false, error: "Invalid date format" };

    const today = new Date();
    if (fieldType === "futureDate" && date <= today) {
      return { isValid: false, error: "Date must be in the future" };
    }
    if (fieldType === "pastDate" && date >= today) {
      return { isValid: false, error: "Date must be in the past" };
    }

    if (nodeData.minDate && date < new Date(nodeData.minDate)) {
      return { isValid: false, error: `Date must be after ${nodeData.minDate}` };
    }
    if (nodeData.maxDate && date > new Date(nodeData.maxDate)) {
      return { isValid: false, error: `Date must be before ${nodeData.maxDate}` };
    }

    return { isValid: true };
  }

  validatePhoneInput(value) {
    const phoneRegex = /^[+]?[1-9]\d{1,14}$/;
    return phoneRegex.test(value.replace(/[\s-()]/g, ""))
      ? { isValid: true }
      : { isValid: false, error: "Invalid phone number format" };
  }

  validateTimeInput(value) {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(value)
      ? { isValid: true }
      : { isValid: false, error: "Invalid time format (HH:MM)" };
  }
}

module.exports = FormPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./formSchema.json");
  schema.category = "form";
  return schema;
}
