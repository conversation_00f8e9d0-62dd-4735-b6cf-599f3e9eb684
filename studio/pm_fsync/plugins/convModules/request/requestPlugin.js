"use strict";
/**
 *  Request Node Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class RequestPlugin
 */
class RequestPlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        const { url, method, headers, body, timeout, channelConfig } = context.process;
        const channelType = context.channelType || 'web';
        
        // Get channel-specific configuration
        const requestConfig = channelConfig?.[channelType] || context.process;
        
        // Build request
        const httpRequest = {
          URL: requestConfig.url || url,
          type: requestConfig.method || method || 'GET',
          headerBody: {
            'Content-Type': 'application/json',
            ...headers,
            ...requestConfig.headers
          },
          requestBody: requestConfig.body || body,
          timeout: requestConfig.timeout || timeout || 10000
        };

        // Make HTTP call
        const response = await this.httpCall(httpRequest);
        
        // Store response in journey context
        this.updateJourneyContext(context, {
          lastRequest: {
            url: httpRequest.URL,
            method: httpRequest.type,
            timestamp: new Date().toISOString()
          },
          lastResponse: response
        });

        // Store in global context
        const globalCtx = this.getGlobalContext(context);
        globalCtx.sessionData[`request_${context.coordinates?.nodeData?.id}`] = response;

        resolve({
          code: error_codes.success,
          response: response.data,
          statusCode: response.code,
          nodeType: "request"
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  close() {}
}

module.exports = RequestPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./requestSchema.json");
  schema.category = "request";
  return schema;
}