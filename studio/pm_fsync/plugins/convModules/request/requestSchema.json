{"typeId": "2.4", "name": "Request", "title": "HTTP Request", "description": "Make HTTP/API requests with channel-specific configurations", "type": "object", "required": ["name", "coordinates", "process", "output"], "properties": {"name": {"description": "Name of the module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates of the module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "process": {"description": "Processing properties of the module", "title": "Process", "type": "object", "required": ["url"], "properties": {"url": {"description": "Request URL", "title": "URL", "type": "string"}, "method": {"description": "HTTP method", "title": "Method", "type": "string", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH"], "default": "GET"}, "headers": {"description": "Request headers", "title": "Headers", "type": "object"}, "body": {"description": "Request body", "title": "Body", "type": "object"}, "timeout": {"description": "Request timeout in milliseconds", "title": "Timeout", "type": "number", "default": 10000}, "channelConfig": {"description": "Channel-specific configurations", "title": "Channel Configuration", "type": "object", "properties": {"web": {"type": "object", "properties": {"url": {"type": "string"}, "method": {"type": "string"}, "headers": {"type": "object"}, "body": {"type": "object"}, "timeout": {"type": "number"}}}, "whatsapp": {"type": "object", "properties": {"url": {"type": "string"}, "method": {"type": "string"}, "headers": {"type": "object"}, "body": {"type": "object"}, "timeout": {"type": "number"}}}}}}}, "output": {"description": "The output params", "type": "object"}}}