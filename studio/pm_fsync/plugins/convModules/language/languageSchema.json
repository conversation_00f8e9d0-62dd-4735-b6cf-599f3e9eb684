{"typeId": "2.10", "name": "Language", "title": "Language Management", "description": "Handle language detection, switching, and management", "type": "object", "required": ["name", "coordinates", "process", "output"], "properties": {"name": {"description": "Name of the module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates of the module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "process": {"description": "Processing properties of the module", "title": "Process", "type": "object", "required": ["action"], "properties": {"action": {"description": "Language action to perform", "title": "Action", "type": "string", "enum": ["set", "detect", "switch"], "default": "set"}, "targetLanguage": {"description": "Target language code", "title": "Target Language", "type": "string", "enum": ["en", "es", "fr", "de", "it", "pt", "ru", "zh", "ja", "ko"], "default": "en"}, "supportedLanguages": {"description": "List of supported languages", "title": "Supported Languages", "type": "array", "items": {"type": "string", "enum": ["en", "es", "fr", "de", "it", "pt", "ru", "zh", "ja", "ko"]}, "default": ["en", "es", "fr"]}, "autoDetect": {"description": "Enable automatic language detection", "title": "Auto Detect", "type": "boolean", "default": false}}}, "output": {"description": "The output params", "type": "object"}}}