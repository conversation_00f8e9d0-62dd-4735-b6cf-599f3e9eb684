"use strict";
/**
 *  Language Node Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class LanguagePlugin
 */
class LanguagePlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        const { action, targetLanguage, supportedLanguages, autoDetect } = context.process;
        
        let result = {};
        
        switch (action) {
          case 'set':
            // Set language for the session
            context.language = targetLanguage;
            const globalCtx = this.getGlobalContext(context);
            globalCtx.sessionData.selectedLanguage = targetLanguage;
            globalCtx.sessionData.languageSetAt = new Date().toISOString();
            result = {
              action: 'set',
              language: targetLanguage,
              message: `Language set to ${targetLanguage}`
            };
            break;
            
          case 'detect':
            // Auto-detect language from user input
            const detectedLanguage = this.detectLanguage(context.userInput, supportedLanguages);
            context.language = detectedLanguage;
            const globalCtx = this.getGlobalContext(context);
            globalCtx.sessionData.detectedLanguage = detectedLanguage;
            globalCtx.sessionData.languageDetectedAt = new Date().toISOString();
            result = {
              action: 'detect',
              language: detectedLanguage,
              confidence: 0.8, // Mock confidence score
              message: `Detected language: ${detectedLanguage}`
            };
            break;
            
          case 'switch':
            // Switch to a different language
            const previousLanguage = context.language;
            context.language = targetLanguage;
            const globalCtx = this.getGlobalContext(context);
            globalCtx.sessionData.previousLanguage = previousLanguage;
            globalCtx.sessionData.selectedLanguage = targetLanguage;
            globalCtx.sessionData.languageSwitchedAt = new Date().toISOString();
            result = {
              action: 'switch',
              previousLanguage,
              currentLanguage: targetLanguage,
              message: `Language switched from ${previousLanguage} to ${targetLanguage}`
            };
            break;
            
          default:
            throw new Error(`Unsupported language action: ${action}`);
        }

        // Store language operation in journey context
        this.updateJourneyContext(context, {
          lastLanguageOperation: result,
          currentLanguage: context.language
        });

        resolve({
          code: error_codes.success,
          ...result,
          nodeType: "language"
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  detectLanguage(text, supportedLanguages) {
    // Simple language detection logic (in real implementation, use proper language detection library)
    if (!text) return 'en';
    
    const languagePatterns = {
      'en': /^[a-zA-Z\s.,!?]+$/,
      'es': /[ñáéíóúü]/i,
      'fr': /[àâäéèêëïîôöùûüÿç]/i,
      'de': /[äöüß]/i,
      'it': /[àèéìíîòóù]/i
    };
    
    for (const [lang, pattern] of Object.entries(languagePatterns)) {
      if (supportedLanguages.includes(lang) && pattern.test(text)) {
        return lang;
      }
    }
    
    return supportedLanguages[0] || 'en';
  }

  close() {}
}

module.exports = LanguagePlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./languageSchema.json");
  schema.category = "language";
  return schema;
}