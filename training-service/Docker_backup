# ---- Stage 1: The Builder ----
# Using a specific version is good practice.
FROM python:3.9-slim as builder

WORKDIR /app

# Install OS-level dependencies first. These rarely change.
RUN apt-get update && apt-get install -y --no-install-recommends build-essential

# Set up a virtual environment. This also rarely changes.
ENV VIRTUAL_ENV=/opt/venv
RUN python -m venv $VIRTUAL_ENV
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# --- THE KEY OPTIMIZATION ---
# Copy ONLY the requirements file. This layer now only depends on this single file.
COPY requirements.txt .

# Run the slowest step: installing dependencies. This layer depends only on the one above.
# It will be cached as long as requirements.txt doesn't change.
RUN pip install --no-cache-dir -r requirements.txt
# ----------------------------


# ---- Stage 2: The Final Production Image ----
FROM python:3.9-slim

WORKDIR /app

RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Copy the pre-installed virtual environment from the builder stage.
COPY --from=builder /opt/venv /opt/venv

# Copy your application source code LAST.
# Changes to your code will now only invalidate this layer and the CMD, which are very fast.
COPY ./src ./src

RUN chown -R appuser:appgroup /app
USER appuser

ENV PATH="/opt/venv/bin:$PATH"
CMD ["python", "-m", "src.main"]