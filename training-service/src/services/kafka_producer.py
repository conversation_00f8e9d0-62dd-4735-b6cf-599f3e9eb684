# src/services/kafka_producer.py
import json
import logging
from kafka import KafkaProducer
from kafka.errors import KafkaError

from src.config import settings

logger = logging.getLogger(__name__)

class TrainingResultProducer:
    def __init__(self):
        kafka_settings = settings.kafka
        self.topic = 'training-results'  # Use the new topic
        try:
            self.producer = KafkaProducer(
                bootstrap_servers=kafka_settings.bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode('utf-8'),
                acks='all',  # Wait for all replicas to acknowledge
                retries=3    # Retry sending on failure
            )
            logger.info(f"KafkaProducer connected for topic '{self.topic}'")
        except KafkaError as e:
            logger.critical(f"Failed to create KafkaProducer: {e}")
            raise

    def send_result(self, payload: dict):
        try:
            future = self.producer.send(self.topic, value=payload)
            # Block for 'synchronous' sends
            record_metadata = future.get(timeout=10)
            logger.info(
                f"Sent training result to topic '{record_metadata.topic}' "
                f"partition {record_metadata.partition} "
                f"with offset {record_metadata.offset}"
            )
        except KafkaError as e:
            logger.error(f"Failed to send training result payload {payload}: {e}")

    def close(self):
        if self.producer:
            self.producer.flush()
            self.producer.close()
            logger.info("KafkaProducer closed.")