# src/services/kafka_consumer.py
import asyncio
import json
import logging
from kafka import KafkaConsumer

from src.config import settings
from src.services.training import TrainingOrchestrator

logger = logging.getLogger(__name__)

class KafkaTrainingConsumer:
    def __init__(self, orchestrator: TrainingOrchestrator):
        self.orchestrator = orchestrator
        kafka_settings = settings.kafka
        
        logger.info(f"Initializing KafkaConsumer for topic '{kafka_settings.topic}' at {kafka_settings.bootstrap_servers}")
        self.consumer = KafkaConsumer(
            kafka_settings.topic,
            bootstrap_servers=kafka_settings.bootstrap_servers,
            value_deserializer=lambda m: json.loads(m.decode('utf-8')),
            auto_offset_reset='earliest',
            group_id=kafka_settings.group_id,
            enable_auto_commit=True,
        )

    def run(self):
        logger.info("Kafka consumer started. Waiting for messages...")
        for message in self.consumer:
            logger.info(f"Received message: {message.value}")
            data = message.value
            bot_id = data.get('botId')
            job_id = data.get('jobId')

            if bot_id and job_id:
                # Each message is a discrete task, so we can run it and wait.
                # For high throughput, you could use a thread/process pool.
                asyncio.run(self.orchestrator.process_job(bot_id, job_id))
            else:
                logger.warning(f"Skipping message due to missing 'botId' or 'jobId': {data}")