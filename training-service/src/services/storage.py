# src/services/storage.py
import logging
import boto3
from botocore.exceptions import Client<PERSON><PERSON>r
from pathlib import Path

from src.config import settings

logger = logging.getLogger(__name__)

class StorageService:
    def __init__(self):
        s3_settings = settings.s3
        self.bucket_name = s3_settings.bucket
        self.s3_client = boto3.client(
            "s3",
            aws_access_key_id=s3_settings.access_key_id,
            aws_secret_access_key=s3_settings.secret_access_key.get_secret_value(),
            region_name=s3_settings.region,
            endpoint_url=s3_settings.endpoint_url,
        )
        logger.info(
            f"StorageService initialized for bucket '{self.bucket_name}' "
            f"at endpoint '{s3_settings.endpoint_url or 'AWS S3'}'"
        )
        # --- NEW BUCKET CREATION LOGIC ---
        self._create_bucket_if_not_exists()
        # ---------------------------------

    def _create_bucket_if_not_exists(self):
        """Checks if a bucket exists and creates it if it does not."""
        try:
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            logger.info(f"Bucket '{self.bucket_name}' already exists.")
        except ClientError as e:
            # If a 404 error is raised, the bucket does not exist.
            if e.response['Error']['Code'] == '404':
                logger.info(f"Bucket '{self.bucket_name}' not found. Creating it...")
                try:
                    self.s3_client.create_bucket(Bucket=self.bucket_name)
                    logger.info(f"Bucket '{self.bucket_name}' created successfully.")
                except ClientError as creation_error:
                    logger.error(f"Failed to create bucket '{self.bucket_name}': {creation_error}")
                    raise creation_error
            else:
                # Re-raise the exception if it's not a 404.
                logger.error(f"Error checking for bucket '{self.bucket_name}': {e}")
                raise

    def upload_file(self, file_path: Path, object_name: str) -> str:
        """Upload a file to an S3 bucket and return its URL."""
        try:
            self.s3_client.upload_file(str(file_path), self.bucket_name, object_name)
            logger.info(f"File {file_path} uploaded to {self.bucket_name}/{object_name}")
            
            # For local MinIO, the URL is based on the endpoint.
            endpoint_url = settings.s3.endpoint_url or f"https://s3.{settings.s3.region}.amazonaws.com"
            return f"{endpoint_url}/{self.bucket_name}/{object_name}"
            
        except ClientError as e:
            logger.error(f"Failed to upload {file_path} to S3: {e}", exc_info=True)
            raise