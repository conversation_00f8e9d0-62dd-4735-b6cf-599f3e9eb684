# src/services/rasa_generator.py

import logging
import yaml
from pathlib import Path
from typing import List

from src.database.models import FaqItems, IntentItems

logger = logging.getLogger(__name__)

# --- HELPER CLASS AND DUMPER CONFIGURATION ---
# 1. Create a custom string class that we can identify later.
class LiteralString(str):
    pass

# 2. Define a function that tells PyYAML how to represent our new class.
def literal_string_representer(dumper, data):
    """
    This function is called by PyYAML whenever it sees an instance of LiteralString.
    It tells it to use the literal block style '|'.
    """
    return dumper.represent_scalar('tag:yaml.org,2002:str', data, style='|')

# 3. Add our new representer to the YAML Dumper.
yaml.add_representer(LiteralString, literal_string_representer)
# ---------------------------------------------

class RasaFileGenerator:
    def __init__(self, bot_id: str, job_id: str, faq_items: List[FaqItems],intent_items: List[IntentItems], base_path: Path):
        self.bot_id = bot_id
        self.job_id = job_id
        self.faq_items = faq_items
        self.intent_items = intent_items
        self.job_path = base_path / bot_id / job_id
        self.data_path = self.job_path / "data"

    def _prepare_directories(self):
        logger.info(f"Creating directories at {self.job_path}")
        self.data_path.mkdir(parents=True, exist_ok=True)

    def _generate_nlu(self):
        # This ensures we ALWAYS have at least two intents if an FAQ item exists.
        nlu_data = {
            'version': '3.1',
            'nlu': [
                {
                    'intent': 'chitchat/ask_capability',
                    'examples': LiteralString(
                        "- what can you do?\n"
                        "- what are you able to do?\n"
                        "- what help do you provide?\n"
                        "- how can you help me?"
                    )
                }
            ]
        }

        for item in self.faq_items:
            intent_name = f"faq_{item.id}"

            # 1. Create the bulleted list of examples.
            bulleted_examples = [f"- {q}" for q in item.questions]

            # 2. Join them into a single multiline string.
            examples_string = "\n".join(bulleted_examples)
            
            # --- THIS IS THE KEY CHANGE ---
            # 3. Cast the final string to our custom LiteralString class.
            nlu_data['nlu'].append({
                'intent': intent_name,
                'examples': LiteralString(examples_string)
            })
            # ----------------------------

        # Add intent items
        for item in self.intent_items:
            intent_name = f"intent_{item.knowledgeUnitId}"
            nlu_data['nlu'].append({
                'intent': intent_name,
                'examples': LiteralString(f"- {item.text}")
            })

        with open(self.data_path / "nlu.yml", 'w') as f:
            # Now, when yaml.dump encounters the 'examples' value, it will see
            # it's a LiteralString and use our custom representer function.
            yaml.dump(nlu_data, f, allow_unicode=True, sort_keys=False)

        logger.info("Generated nlu.yml")

    def _generate_domain(self):
        # Start with the corresponding chitchat intent and response
        intents = ['chitchat/ask_capability']
        responses = {
            'utter_chitchat/ask_capability': [
                {'text': "I can answer frequently asked questions."}
            ]
        }

        # Add FAQ intents and responses from the DB
        for item in self.faq_items:
            intent_name = f"faq_{item.id}"
            intents.append(intent_name)
            responses[f"utter_{intent_name}"] = [{"text": item.answer}]
        
        domain_data = {
            'version': '3.1',
            'intents': intents,
            'responses': responses,
            'session_config': {'session_expiration_time': 60, 'carry_over_slots_to_new_session': True},
        }
        with open(self.job_path / "domain.yml", 'w') as f:
            yaml.dump(domain_data, f, allow_unicode=True, sort_keys=False)
        logger.info("Generated domain.yml")

    def _generate_rules(self):
        # Start with the corresponding chitchat rule
        rules = {
            'version': '3.1',
            'rules': [
                {
                    'rule': 'Answer what bot can do',
                    'steps': [
                        {'intent': 'chitchat/ask_capability'},
                        {'action': 'utter_chitchat/ask_capability'}
                    ]
                }
            ]
        }

        # Add FAQ rules from the DB
        for item in self.faq_items:
            intent_name = f"faq_{item.id}"
            rules['rules'].append({
                'rule': f"Respond to {intent_name}",
                'steps': [{'intent': intent_name}, {'action': f"utter_{intent_name}"}]
            })
        with open(self.data_path / "rules.yml", 'w') as f:
            yaml.dump(rules, f, allow_unicode=True, sort_keys=False)
        logger.info("Generated rules.yml")

    def _generate_config(self):
        """
        Generates a lightweight config.yml suitable for low-data FAQ bots.
        This configuration uses ResponseSelector for the main FAQ task.
        """
        config_data = {
            'recipe': 'default.v1',
            'language': 'en',

            # Pipeline for NLU. This is much simpler now.
            'pipeline': [
                # 1. Basic text processing
                {'name': 'WhitespaceTokenizer'},
                {'name': 'RegexFeaturizer'},
                {'name': 'CountVectorsFeaturizer'},
                {'name': 'CountVectorsFeaturizer', 'analyzer': 'char_wb', 'min_ngram': 1, 'max_ngram': 4},

                # 2. The key component for our FAQ task.
                #    It will handle all intents that look like "faq/..."
                {'name': 'ResponseSelector', 'epochs': 50},

                # 3. A very lightweight DIETClassifier to handle any *non-FAQ*
                #    intents like 'greet' or 'goodbye'. We set epochs low
                #    to ensure it trains fast and doesn't fail on low data.
                {'name': 'DIETClassifier', 'epochs': 20},
            ],

            # Policies for Core. We only need the RulePolicy for this simple bot.
            'policies': [
                {'name': 'RulePolicy'}
            ]
        }
        with open(self.job_path / "config.yml", 'w') as f:
            yaml.dump(config_data, f, sort_keys=False, allow_unicode=True)
        logger.info("Generated lightweight config.yml using ResponseSelector")

    def run(self) -> Path:
        self._prepare_directories()
        self._generate_nlu()
        self._generate_domain()
        self._generate_rules()
        self._generate_config()
        return self.job_path