# src/database/manager.py
import logging
from contextlib import contextmanager
from typing import Generator, List

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

from .models import IntentItems, TrainingJob, FaqItems, KnowledgeUnit, Bot
from src.config import settings

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self, db_uri: str):
        self.engine = create_engine(db_uri)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        logger.info("DatabaseManager initialized.")

    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session rolled back due to: {e}", exc_info=True)
            raise
        finally:
            session.close()

    def update_job_status(self, job_id: str, status: str, model_url: str = None, error_message: str = None):
        with self.get_session() as session:
            job = session.query(TrainingJob).filter_by(id=job_id).one_or_none()
            if not job:
                logger.error(f"Attempted to update a non-existent job with ID: {job_id}")
                return

            job.status = status
            if model_url:
                job.modelUrl = model_url
            if error_message:
                job.errorMessage = error_message
            logger.info(f"Updated job {job_id} to status '{status}'")

    def fetch_faq_data(self, bot_id: str) -> List[FaqItems]:
        with self.get_session() as session:
            logger.info(f"Fetching FAQ data for bot {bot_id}")
            faq_items = (
                session.query(FaqItems)
                .join(KnowledgeUnit, FaqItems.knowledgeUnitId == KnowledgeUnit.id)
                .join(Bot, KnowledgeUnit.botId == Bot.id)
                .filter(Bot.id == bot_id)
                .all()
            )
            logger.info(f"Fetched {len(faq_items)} FAQ items for bot {bot_id}")
            # Detach entities from the session to prevent circular references
            session.expunge_all()
            return faq_items
        
    def fetch_intent_knowledge_units(self, bot_id: str) -> List[IntentItems]:
        with self.get_session() as session:
            logger.info(f"Fetching intent data for bot {bot_id}")
            intent_items = (
                session.query(KnowledgeUnit)
                .join(Bot, KnowledgeUnit.botId == Bot.id)
                .filter(Bot.id == bot_id)
                .filter(KnowledgeUnit.type == 'intent')
                .all()
            )
            logger.info(f"Fetched {len(intent_items)} intent items for bot {bot_id}")
            # Detach entities from the session to prevent circular references
            session.expunge_all()
            return intent_items
        
    def fetch_intent_data(self, bot_id: str, knowledge_unit_ids: List[int]) -> List[IntentItems]:
        with self.get_session() as session:
            logger.info(f"Fetching intent data for bot {bot_id}")
            intent_items = (
                session.query(IntentItems)
                .join(KnowledgeUnit, IntentItems.knowledgeUnitId == KnowledgeUnit.id)
                .join(Bot, KnowledgeUnit.botId == Bot.id)
                .filter(Bot.id == bot_id)
                .filter(KnowledgeUnit.id.in_(knowledge_unit_ids))
                .all()
            )
            logger.info(f"Fetched {len(intent_items)} intent items for bot {bot_id}")
            # Detach entities from the session to prevent circular引用
            session.expunge_all()
            return intent_items