# src/database/models.py
from os import name
from sqlalchemy import (JSON, Column, DateTime, Enum,
                        ForeignKey, String, Text)
from sqlalchemy.orm import relationship, declarative_base
from datetime import datetime

Base = declarative_base()

class Bot(Base):
    __tablename__ = 'bots'
    id = Column(String(36), primary_key=True)
    name = Column(String(255), nullable=False)
    knowledge_units = relationship("KnowledgeUnit", back_populates="bot")

class KnowledgeUnit(Base):
    __tablename__ = 'knowledge_units'
    id = Column(String(36), primary_key=True)
    botId = Column(String(36), ForeignKey('bots.id'), nullable=False)
    type = Column(String(50), nullable=False)
    name = Column(String(255))
    bot = relationship("Bot", back_populates="knowledge_units")
    faq_items = relationship("FaqItems", back_populates="knowledge_unit")
    intent_items = relationship("IntentItems", back_populates="knowledge_unit")
class IntentItems(Base):
    __tablename__ = 'intent_items'
    id = Column(String(36), primary_key=True)
    knowledgeUnitId = Column(String(36), ForeignKey('knowledge_units.id'), nullable=False)
    text = Column(Text, nullable=False)
    entities = Column(JSON, default=[])
    knowledge_unit = relationship("KnowledgeUnit", back_populates="intent_items")

class FaqItems(Base):
    __tablename__ = 'faq_items'
    id = Column(String(36), primary_key=True)
    knowledgeUnitId = Column(String(36), ForeignKey('knowledge_units.id'), nullable=False)
    questions = Column(JSON, nullable=False)
    answer = Column(Text, nullable=False)
    knowledge_unit = relationship("KnowledgeUnit", back_populates="faq_items")

class TrainingJob(Base):
    __tablename__ = 'training_jobs'
    id = Column(String(36), primary_key=True)
    botId = Column(String(36), nullable=False)
    status = Column(Enum('pending', 'running', 'completed', 'failed', 'cancelled'), default='pending')
    modelUrl = Column(String(1024))
    errorMessage = Column(Text)
    updatedAt = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)