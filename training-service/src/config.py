# src/config.py
from pathlib import Path
from typing import Optional

from pydantic import BaseSettings, SecretStr, Field


class KafkaSettings(BaseSettings):
    bootstrap_servers: str = "localhost:9092"
    topic: str = "training-requests"
    group_id: str = "rasa-training-group"

    class Config:
        env_prefix = "KAFKA_"


class DatabaseSettings(BaseSettings):
    user: str
    password: SecretStr
    host: str

    port: int
    name: str

    def get_uri(self) -> str:
        return f"mysql+pymysql://{self.user}:{self.password.get_secret_value()}@{self.host}:{self.port}/{self.name}"

    class Config:
        env_prefix = "DB_"


class S3Settings(BaseSettings):
    access_key_id: str
    secret_access_key: SecretStr
    bucket: str
    region: str
    endpoint_url: Optional[str] = None  # For MinIO or other S3-compatibles

    class Config:
        env_prefix = "S3_"


class AppSettings(BaseSettings):
    log_level: str = "INFO"
    training_data_path: Path = Path("training_data")
    models_output_path: Path = Path("models")

    # Nested settings
    kafka: KafkaSettings = Field(default_factory=KafkaSettings)
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    s3: S3Settings = Field(default_factory=S3Settings)

    class Config:
        # This allows loading from a .env file
        env_file = ".env"
        env_file_encoding = "utf-8"


# Create a single settings instance to be imported
settings = AppSettings()