# src/main.py
import logging

from dotenv import load_dotenv
load_dotenv()

from src.config import settings
from src.database.manager import DatabaseManager
from src.services.kafka_consumer import KafkaTrainingConsumer
from src.services.storage import StorageService
from src.services.training import TrainingOrchestrator
from src.services.kafka_producer import TrainingResultProducer
from src.utils.logging_config import setup_logging

def main():
    # 1. Setup Logging
    setup_logging(settings.log_level)
    logger = logging.getLogger(__name__)
    logger.info("Starting Training Server")

    # 2. Ensure base directories exist
    settings.training_data_path.mkdir(exist_ok=True)
    settings.models_output_path.mkdir(exist_ok=True)
    logger.info(f"Temp training data path: {settings.training_data_path.resolve()}")
    logger.info(f"Temp models output path: {settings.models_output_path.resolve()}")

    # 3. Initialize services (Dependency Injection)
    result_producer = None
    try:
        db_manager = DatabaseManager(settings.database.get_uri())
        storage_service = StorageService()
        result_producer = TrainingResultProducer()
        training_orchestrator = TrainingOrchestrator(db_manager, storage_service, result_producer)
        consumer = KafkaTrainingConsumer(training_orchestrator)
    except Exception as e:
        logger.critical(f"Failed to initialize services. Shutting down. Error: {e}", exc_info=True)
        return

    # 4. Start the consumer loop
    try:
        consumer.run()
    except KeyboardInterrupt:
        logger.info("Service interrupted by user. Shutting down.")
    except Exception as e:
        logger.critical(f"An unhandled exception occurred in the consumer loop: {e}", exc_info=True)
    finally:
        if result_producer:
            result_producer.close()
        logger.info("Training Server has shut down.")


if __name__ == "__main__":
    main()