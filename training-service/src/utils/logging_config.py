# src/utils/logging_config.py
import logging
import sys

def setup_logging(log_level: str = "INFO"):
    """
    Configures logging for the application.
    """
    log_formatter = logging.Formatter(
        "%(asctime)s - [%(levelname)s] - %(name)s - (%(filename)s).%(funcName)s(%(lineno)d) - %(message)s"
    )
    
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(log_formatter)
    
    logging.basicConfig(
        level=log_level,
        handlers=[handler]
    )
    logging.info(f"Logging configured with level: {log_level}")