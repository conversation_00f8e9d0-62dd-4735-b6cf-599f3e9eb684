# Rasa Training Service

This service is a dedicated, asynchronous worker responsible for training Rasa models. It listens for training requests on a Kafka topic, fetches the required training data from a central database, generates the necessary Rasa files, trains the model, uploads the final artifact to an S3-compatible object store (like MinIO), and publishes the result back to another Kafka topic.

## Features

- **Asynchronous Processing:** Listens to Kafka for training jobs, allowing the main API to remain responsive.
- **Decoupled Architecture:** Communicates with other services via Kafka, ensuring resilience and scalability.
- **Data-Driven Configuration:** Generates Rasa files (`nlu.yml`, `domain.yml`, etc.) dynamically from data stored in a database.
- **Artifact Storage:** Uploads trained models to an S3-compatible object store for persistence and easy retrieval.
- **Robust Feedback Loop:** Publishes success or failure results back to a Kafka topic for other services to consume.
- **Containerized:** Fully containerized with Docker and Docker Compose for easy and consistent local development and production deployment.

## Prerequisites

- **Docker** and **Docker Compose:** Required to run the local development environment (Kafka, MinIO, etc.).
- **Python 3.9+** and `pip`: For running the service locally without Docker.
- **A running instance of the database** that the service can connect to.

## Getting Started: Local Development

Follow these steps to set up and run the training service on your local machine.

### 1. Clone the Repository

If you haven't already, clone the project containing this service.

### 2. Configure Environment Variables

The service is configured using environment variables. A template is provided.

-   Copy the example environment file:
    ```bash
    cp .env.example .env
    ```
-   Open the newly created `.env` file and update the following values:
    -   `DB_USER`, `DB_PASSWORD`, `DB_HOST`, `DB_PORT`, `DB_NAME`: Your database connection details.
    -   `S3_ACCESS_KEY_ID`, `S3_SECRET_ACCESS_KEY`: These should match the `MINIO_ROOT_USER` and `MINIO_ROOT_PASSWORD` in the `docker-compose.yml` file.

### 3. Start Dependent Services (Kafka & MinIO)

This project uses Docker Compose to manage local instances of Kafka and MinIO.

-   From the root of the `training-service` directory, start the services in the background:
    ```bash
    docker-compose up -d
    ```
-   **To verify they are running:**
    ```bash
    docker-compose ps
    ```
    You should see `kafka`, `zookeeper`, and `minio` with a `State` of `Up`.

### 4. Set Up MinIO Bucket (One-Time Setup)

The first time you run MinIO, you need to configure the bucket to allow public access to the trained models.

1.  Open your web browser and navigate to the MinIO console: **[http://localhost:9001](http://localhost:9001)**.
2.  Log in with the credentials from your `.env` file (e.g., Access Key: `minio-access-key`, Secret Key: `minio-secret-key`).
3.  On the left menu, go to **Buckets**.
4.  The bucket (`rasa-models-local` by default) will be created automatically when the service starts. If you see it, click on it. If not, proceed to the next step and it will be created.
5.  Click **Manage**, then go to the **Access Policy** tab.
6.  Change the bucket's access policy from **Private** to **Public**. This allows the trained models to be downloaded via their URL.

### 5. Install Python Dependencies

It is highly recommended to use a Python virtual environment.

```bash
# Create a virtual environment (only needs to be done once)
python3 -m venv venv

# Activate the virtual environment
source venv/bin/activate

# Install the required packages
pip install -r requirements.txt
```

### 6. Run the Training Service

With the dependent services running and the virtual environment activated, start the Python application.

-   Run the application as a module from the project root to ensure all imports work correctly:
    ```bash
    python -m src.main
    ```
-   The service will start, connect to Kafka, and log `Kafka consumer started. Waiting for messages...`. It is now ready to receive training jobs.

### 7. Triggering a Job

To test the service, a message must be published to the `training-requests` Kafka topic by another service (e.g., the `bot-builder-service`). The message must be a JSON object with the following structure:

```json
{
  "botId": "your-bot-uuid",
  "jobId": "a-new-job-uuid"
}
```

Upon receiving this message, the training service will begin its process, and you will see detailed logs in your terminal. The final trained model (`.tar.gz`) will appear in your MinIO bucket.

### 8. Stopping the Environment

-   To stop the Python application, press `Ctrl+C` in its terminal window.
-   To stop the Kafka and MinIO Docker containers, run:
    ```bash
    docker-compose down
    ```

## Running with Docker

You can also run the training service itself as a Docker container.

1.  **Build the image:**
    ```bash
    docker build -t training-service .
    ```
2.  **Run the container:**
    Ensure your dependent services from `docker-compose.yml` are running. Then, run the training service container and connect it to the same network.

    ```bash
    # Find the network name
    # docker network ls | grep training-service

    # Run the container (replace 'training-service_default' with your actual network name)
    docker run --rm -it \
      --name rasa-trainer \
      --network training-service_default \
      --env-file .env \
      training-service
    ```
    *Note: For this to work, your `.env` file must be updated to use the Docker service names instead of `localhost` (e.g., `DB_HOST=mysql`, `KAFKA_BOOTSTRAP_SERVERS=kafka:9092`, `S3_ENDPOINT_URL=http://minio:9000`).*